// Register service worker for PWA functionality with improved error handling
if ('serviceWorker' in navigator) {
  // Wait until the page is fully loaded
  window.addEventListener('load', () => {
    // First, check if there's an existing service worker and unregister it
    // This helps prevent issues with stale service workers
    navigator.serviceWorker.getRegistrations().then(registrations => {
      const unregisterPromises = registrations.map(registration => {
        // Unregistering existing service worker
        return registration.unregister();
      });

      return Promise.all(unregisterPromises);
    }).then(() => {
      // After unregistering old service workers, register the new one

      // Add a timeout to prevent hanging registrations
      const registerPromise = navigator.serviceWorker.register('/sw-custom.js');
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Service worker registration timed out')), 10000);
      });

      return Promise.race([registerPromise, timeoutPromise]);
    }).then(registration => {
      // Service Worker registered successfully

      // Check for updates to the service worker
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;

        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available

            // Show a notification to the user that new content is available
            if (window.confirm('New version of the app is available! Reload to update?')) {
              // Clear caches before reloading to ensure fresh content
              if ('caches' in window) {
                caches.keys().then(cacheNames => {
                  return Promise.all(
                    cacheNames.map(cacheName => {
                      return caches.delete(cacheName);
                    })
                  );
                }).then(() => {
                  window.location.reload();
                });
              } else {
                window.location.reload();
              }
            }
          }
        });
      });

      // Handle service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        // Service Worker controller changed, refreshing content
      });
    }).catch(error => {
      // Service Worker registration failed - continue without PWA features
    });
  });
}

// Handle PWA installation
let deferredPrompt;

// Function to set up the install button
function setupInstallButton() {
  const installBtn = document.getElementById('installBtn');
  if (!installBtn) {
    setTimeout(setupInstallButton, 1000);
    return;
  }

  // Add click event listener to install button
  installBtn.addEventListener('click', async () => {
    if (!deferredPrompt) {
      return;
    }

    // Hide the install button once clicked
    installBtn.style.display = 'none';

    // Show the install prompt
    deferredPrompt.prompt();

    // Wait for the user's response
    const { outcome } = await deferredPrompt.userChoice;

    // Reset the deferred prompt variable since it can only be used once
    deferredPrompt = null;
  });

  // Check if the app is already installed
  if (window.matchMedia('(display-mode: standalone)').matches) {
    installBtn.style.display = 'none';
  }
}

// Listen for the beforeinstallprompt event
window.addEventListener('beforeinstallprompt', (e) => {
  // Prevent the mini-infobar from appearing on mobile
  e.preventDefault();

  // Save the event so it can be triggered later
  deferredPrompt = e;

  // Show the install button
  const installBtn = document.getElementById('installBtn');
  if (installBtn) {
    installBtn.style.display = 'flex';
  }
});

// Add to home screen for iOS
const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
const isStandalone = window.matchMedia('(display-mode: standalone)').matches;

if (isIOS && !isStandalone) {
  // For iOS, we could show a custom instruction banner here
  // This would be a good place to add code for an iOS installation guide
}

// Set up the install button when the DOM is fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupInstallButton);
} else {
  setupInstallButton();
}