"use client";
import { SignUp } from "@clerk/nextjs";
import Image from "next/image";
import Link from "next/link";

export default function SignUpPage() {
  return (
    <div className="min-h-screen mt-[-6em] pt-[6em] min-md:overflow-y-hidden flex flex-col items-center justify-center bg-gradient-to-b from-white to-gray-100 p-4">
      <div className="w-full flex max-md:flex-col justify-around gap-8 p-9 items-top max-md:items-center">
        <div className="text-center w-[50vw] h-[50vh] slef-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/q 10.png"
              alt="KROTI Logo"
              width={700}
              height={700}
              className="mx-auto mb-4 object-contain"
            />
          </Link>
        </div>

        <div className="bg-white rounded-xl overflow-hidden">
          <div className="p-1 clerk">
            <SignUp
              appearance={{
                elements: {
                  formButtonPrimary:
                    "bg-[#4DA1A9] hover:bg-[#3C8A92] text-sm normal-case",
                  footerActionLink:
                    "text-[#4DA1A9] hover:text-[#3C8A92]",
                  card: "shadow-none",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  socialButtonsBlockButton:
                    "border border-gray-300 text-gray-600",
                  socialButtonsBlockButtonText:
                    "font-normal text-sm",
                  formFieldInput:
                    "border border-gray-300 focus:border-[#4DA1A9] focus:ring-[#4DA1A9]",
                  formFieldLabel:
                    "text-gray-700",
                  identityPreviewEditButton:
                    "text-[#4DA1A9] hover:text-[#3C8A92]",
                }
              }}
              routing="path"
              path="/sign-up"
              signInUrl="/sign-in"
            />
          </div>
          <div className="text-center m-8">
            <p className="text-gray-600">
              Already have an account?{" "}
              <Link href="/sign-in" className="text-[#4DA1A9] hover:text-[#3C8A92] font-medium">
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
