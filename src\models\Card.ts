// src/models/Card.ts
import mongoose, { Document, Model, Schema } from 'mongoose';

export interface ICard extends Document {
  userId: string;
  name: string;
  title: string;
  company?: string;
  phone?: string;
  email?: string;
  address?: string;
  website?: string;
  qrCode?: string;
  rawText?: string;
  imageUrl?: string;  // New field for storing the image URL
  notes?: string;     // Optional notes about the contact
  createdAt: Date;
  updatedAt: Date;
}

const CardSchema: Schema = new Schema({
  userId: { type: String, required: true },
  name: { type: String, required: true },
  title: { type: String, required: true },
  company: { type: String },
  phone: { type: String },
  email: { type: String },
  address: { type: String },
  website: { type: String },
  qrCode: { type: String },
  rawText: { type: String },
  imageUrl: { type: String },  // Added imageUrl field
  notes: { type: String },     // Optional notes about the contact
}, { timestamps: true });

// Database model export - using dynamic model creation
const modelName = 'Card';
export const Card: Model<ICard> = mongoose.models[modelName] || mongoose.model<ICard>(modelName, CardSchema);
