"use client";
import { UserProfile } from "@clerk/nextjs";
import Image from "next/image";
import Link from "next/link";

export default function UserProfilePage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-white to-gray-100 p-4">
      <div className="w-full max-w-4xl">

        
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="p-1">
            <UserProfile 
              appearance={{
                elements: {
                  navbar: "hidden",
                  card: "shadow-none",
                  headerTitle: "text-2xl font-bold text-gray-800",
                  headerSubtitle: "text-gray-600",
                  formButtonPrimary: 
                    "bg-[#4DA1A9] hover:bg-[#3C8A92] text-sm normal-case",
                  formButtonReset: 
                    "bg-gray-200 hover:bg-gray-300 text-gray-800 text-sm normal-case",
                  formFieldInput: 
                    "border border-gray-300 focus:border-[#4DA1A9] focus:ring-[#4DA1A9]",
                  formFieldLabel: 
                    "text-gray-700",
                  userPreviewMainIdentifier: 
                    "text-xl font-semibold text-gray-800",
                  userPreviewSecondaryIdentifier: 
                    "text-gray-600",
                  userButtonPopoverActionButton: 
                    "text-gray-700 hover:text-[#4DA1A9] hover:bg-gray-100",
                  userButtonPopoverActionButtonIcon: 
                    "text-gray-500",
                  userButtonPopoverFooter: 
                    "border-t border-gray-200 pt-2 mt-2",
                }
              }}
              routing="path"
              path="/user-profile"
            />
          </div>
        </div>
        
        <div className="text-center mt-8">
          <Link href="/dashboard" className="text-[#4DA1A9] hover:text-[#3C8A92] font-medium">
            Back to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}
