// Check if Font Awesome is loaded and provide fallback
(function() {
  'use strict';
  
  // Wait for DOM to be ready
  function checkFontAwesome() {
    // Check if Font Awesome CSS is loaded
    const fontAwesomeLoaded = Array.from(document.styleSheets).some(sheet => {
      try {
        return sheet.href && sheet.href.includes('font-awesome');
      } catch (e) {
        return false;
      }
    });
    
    if (!fontAwesomeLoaded) {
      // Font Awesome failed to load, add fallback styles
      const fallbackCSS = `
        .fa-solid:before, .fa-regular:before, .fa-light:before, .fa-thin:before, .fa-duotone:before, .fa-brands:before {
          font-family: Arial, sans-serif;
          font-weight: normal;
        }
        .fa-camera:before { content: "📷"; }
        .fa-upload:before { content: "⬆️"; }
        .fa-download:before { content: "⬇️"; }
        .fa-rotate-right:before { content: "🔄"; }
        .fa-expand:before { content: "⛶"; }
        .fa-compress:before { content: "⛶"; }
        .fa-camera-slash:before { content: "🚫"; }
        .fa-share:before { content: "📤"; }
        .fa-edit:before { content: "✏️"; }
        .fa-trash:before { content: "🗑️"; }
        .fa-search:before { content: "🔍"; }
        .fa-plus:before { content: "+"; }
        .fa-times:before { content: "×"; }
        .fa-check:before { content: "✓"; }
        .fa-user:before { content: "👤"; }
        .fa-cog:before { content: "⚙️"; }
        .fa-home:before { content: "🏠"; }
        .fa-bars:before { content: "☰"; }
      `;
      
      const style = document.createElement('style');
      style.textContent = fallbackCSS;
      document.head.appendChild(style);
    }
  }
  
  // Check when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkFontAwesome);
  } else {
    checkFontAwesome();
  }
  
  // Also check after a delay to catch late-loading stylesheets
  setTimeout(checkFontAwesome, 2000);
})();
