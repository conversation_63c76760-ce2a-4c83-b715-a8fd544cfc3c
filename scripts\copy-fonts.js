// scripts/copy-fonts.js
const fs = require('fs');
const path = require('path');

// This script copies font files to ensure they're properly served

function copyFonts() {
  try {
    // Source font file
    const sourceFont = path.join(process.cwd(), 'public', 'Fonts', 'CC Wild Words Roman.ttf');
    
    // Create destination directory if it doesn't exist
    const destDir = path.join(process.cwd(), '.next', 'Fonts');
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    
    // Destination path
    const destFont = path.join(destDir, 'CC Wild Words Roman.ttf');
    
    // Copy the file
    fs.copyFileSync(sourceFont, destFont);
    
    console.log('✅ Successfully copied font files to .next directory');
  } catch (error) {
    console.error('❌ Error copying font files:', error);
  }
}

// Execute the function
copyFonts();
