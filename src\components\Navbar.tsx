"use client";
import React, { Suspense } from "react";
import Link from "next/link";
import {
  SignedIn,
  SignedOut,
  UserButton,
} from "@clerk/nextjs";

// Loading component for Navbar
const NavbarLoading = () => (
  <header className="flex z-50 sticky top-0 justify-between items-center gap-4 min-h-16 bg-[#ffffff] flex-wrap">
    <p className="text-3xl max-md:text-2xl flex justify-center gap-2 items-center p-4 font-black leading-none text-gray-900 select-none">
      <img className="h-[50px] object-contain" src="/icons/ocr.png" alt="KROTI" width={50} height={50} />
      <Link href="/">KROTI</Link>
    </p>
    <div className="flex-grow"></div>
    <div className="flex gap-4 p-6 max-md:p-4">
      <div className="animate-pulse h-10 w-20 bg-gray-200 rounded-full"></div>
    </div>
  </header>
);

// Navbar content component
const NavbarContent = () => {
  return (
    <>
      <header className="flex z-50 sticky top-0 justify-between items-center gap-4 min-h-16 bg-[#ffffff] flex-wrap">
        <p className="text-3xl max-md:text-2xl flex justify-center gap-2 items-center p-4 font-black leading-none text-gray-900 select-none">
          <img className="h-[50px] object-contain" src="/icons/ocr.png" alt="KROTI" width={50} height={50} />
          <Link href="/">KROTI</Link>
        </p>

        {/* Search functionality moved to dashboard page */}
        <div className="flex-grow"></div>

        <div className="flex gap-4 p-6 max-md:p-4">
          <div className="flex gap-2">
            <SignedOut>
              <Link href="/sign-in">
                <button className="bg-[#4DA1A9] text-white font-bold rounded-full text-sm py-3 px-4 uppercase tracking-wider transition-all duration-500 cursor-pointer hover:scale-105 hover:bg-[#3C8A92]">
                  SIGN IN
                </button>
              </Link>
              <Link href="/sign-up">
                <button className="outline outline-[#4DA1A9] text-[#4DA1A9] font-bold rounded-full text-sm py-3 px-4 uppercase tracking-wider transition-all duration-500 cursor-pointer hover:scale-105 hover:bg-gray-50">
                  SIGN UP
                </button>
              </Link>
            </SignedOut>
            <SignedIn>
              <UserButton
                userProfileUrl="/user-profile"
                appearance={{
                  elements: {
                    userButtonAvatarBox: "w-10 h-10",
                    userButtonTrigger: "focus:shadow-none",
                    userButtonPopoverCard: "shadow-lg rounded-xl border-0",
                    userButtonPopoverActionButton: "hover:bg-gray-100 hover:text-[#4DA1A9]",
                    userButtonPopoverActionButtonIcon: "text-gray-500",
                    userButtonPopoverFooter: "border-t border-gray-200 pt-2 mt-2",
                  }
                }}
              />
            </SignedIn>
          </div>
        </div>
      </header>
      <hr />
    </>
  );
};

// Main Navbar component with Suspense
const Navbar: React.FC = () => {
  return (
    <Suspense fallback={<NavbarLoading />}>
      <NavbarContent />
    </Suspense>
  );
};

export default Navbar;
