// app/api/proxy/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();

    // Forward request to Hugging Face
    const response = await fetch(
      'codic/Business-Card-Scanner-To-Csv-gredio/process_single',
      {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${process.env.HF_TOKEN}` // If needed
        }
      }
    );

    const data = await response.json();
    return NextResponse.json(data);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return NextResponse.json(
      { error: 'Proxy request failed' },
      { status: 500 }
    );
  }
}