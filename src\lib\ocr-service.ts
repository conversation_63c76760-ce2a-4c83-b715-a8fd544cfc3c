import { Client } from "@gradio/client";

interface OCRResult {
  text: string;
  entities: Record<string, string | string[]>;
}

export class OCRService {
  private static client: Client | null = null;

  static async initialize() {
    if (!this.client) {
      try {
        // Try connecting using just the identifier and an optional Hugging Face token
        this.client = await Client.connect(
          "codic/paddle-ocr-demo",

        );
        // OCR service connected successfully
      } catch (error) {
        console.error("Failed to connect to OCR service:", error);
        throw new Error("Failed to initialize OCR service");
      }
    }
  }

  static async scanSingle(file: File): Promise<OCRResult> {
    await this.initialize();

    if (!this.client) {
      throw new Error("OCR service not initialized");
    }

    try {
      const result = await this.client.predict("/predict", {
        img: file,
        confidence: 0.4,
      });

      // Assuming result.data is an array: [text, entities, unused, error]
      const data = result.data as unknown as [string, Record<string, string | string[]>, unknown, string | null];
      const [text, entities, _unused, error] = data;
      // Third element is unused but necessary for destructuring

      if (error) {
        throw new Error(typeof error === "string" ? error : "OCR processing failed");
      }

      return {
        text: typeof text === "string" ? text : "",
        entities: entities || {},
      };
    } catch (error) {
      console.error("OCR processing error:", error);
      throw new Error("Failed to process document");
    }
  }
}
