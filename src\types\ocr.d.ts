// src/types/ocr.d.ts
export interface OCRResponse {
    success: boolean;
    data?: {
      text: string;
      entities: Record<string, string>;
    };
    error?: string;
  }

  export interface OCRResults {
    name?: string;
    title?: string;
    phone?: string;
    email?: string;
    company?: string;
    address?: string;
    website?: string;
    qrCode?: string;
    rawText?: string;
    imageUrl?: string;
    _id?: string;
  }