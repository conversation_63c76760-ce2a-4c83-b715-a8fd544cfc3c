// src/lib/error-handler.ts
export class ErrorHandler {
  /**
   * Sanitizes error messages to remove technology stack information
   * and provide user-friendly messages
   */
  static sanitizeError(error: unknown): string {
    if (typeof error === 'string') {
      return this.sanitizeMessage(error);
    }
    
    if (error instanceof Error) {
      return this.sanitizeMessage(error.message);
    }
    
    return "An unexpected error occurred. Please try again.";
  }

  /**
   * Sanitizes specific error messages to remove technology references
   */
  private static sanitizeMessage(message: string): string {
    // Remove technology stack references
    const sanitizedMessage = message
      .replace(/next\.js|nextjs|react|mongodb|mongoose|gradio|clerk|vercel|cloudinary/gi, 'system')
      .replace(/chunk.*loading.*error/gi, 'loading error')
      .replace(/failed to connect to gradio/gi, 'service temporarily unavailable')
      .replace(/ocr.*processing.*failed/gi, 'processing failed')
      .replace(/mongodb.*connection/gi, 'database connection')
      .replace(/api.*error/gi, 'service error')
      .replace(/webpack.*chunk/gi, 'loading')
      .replace(/_next\/static\/chunks/gi, 'application resources')
      .replace(/service worker/gi, 'background service');

    // Provide generic user-friendly messages for common errors
    if (sanitizedMessage.toLowerCase().includes('network')) {
      return "Network connection issue. Please check your internet connection and try again.";
    }
    
    if (sanitizedMessage.toLowerCase().includes('permission')) {
      return "Permission denied. Please check your browser settings and try again.";
    }
    
    if (sanitizedMessage.toLowerCase().includes('not found')) {
      return "Resource not found. Please try again.";
    }
    
    if (sanitizedMessage.toLowerCase().includes('timeout')) {
      return "Request timed out. Please try again.";
    }
    
    if (sanitizedMessage.toLowerCase().includes('unauthorized')) {
      return "Authentication required. Please sign in and try again.";
    }

    // If message is still too technical, provide a generic message
    if (this.isTechnicalMessage(sanitizedMessage)) {
      return "Something went wrong. Please try again.";
    }
    
    return sanitizedMessage;
  }

  /**
   * Checks if a message contains technical jargon
   */
  private static isTechnicalMessage(message: string): boolean {
    const technicalTerms = [
      'stack trace', 'exception', 'undefined', 'null', 'promise', 'async',
      'fetch', 'xhr', 'cors', 'json', 'parse', 'stringify', 'buffer',
      'stream', 'socket', 'tcp', 'http', 'ssl', 'tls', 'certificate',
      'token', 'jwt', 'oauth', 'api key', 'endpoint', 'middleware',
      'component', 'hook', 'state', 'props', 'render', 'dom', 'virtual'
    ];
    
    const lowerMessage = message.toLowerCase();
    return technicalTerms.some(term => lowerMessage.includes(term));
  }

  /**
   * Logs errors internally while showing sanitized messages to users
   */
  static logAndSanitize(error: unknown, context?: string): string {
    // Log the full error for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.error(`Error in ${context || 'application'}:`, error);
    }
    
    return this.sanitizeError(error);
  }

  /**
   * Creates user-friendly error responses for API routes
   */
  static createApiErrorResponse(error: unknown, defaultMessage?: string) {
    const sanitizedMessage = this.sanitizeError(error);
    return {
      success: false,
      error: defaultMessage || sanitizedMessage
    };
  }
}
