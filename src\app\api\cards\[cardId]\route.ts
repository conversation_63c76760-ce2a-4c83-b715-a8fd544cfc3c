// src/app/api/cards/[cardId]/route.ts
import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import { Card } from "@/models/Card";
import sharp from "sharp";
import { v2 as cloudinary, UploadApiResponse } from "cloudinary";
import { Error<PERSON>and<PERSON> } from "@/lib/error-handler";

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Helper function to handle card deletion
async function deleteCard(cardId: string) {
  try {
    await connectDB();

    // Find the card first to check ownership
    const card = await Card.findById(cardId);
    if (!card) {
      return NextResponse.json(
        { success: false, error: "Card not found" },
        { status: 404 }
      );
    }

    // Delete the card
    await Card.findByIdAndDelete(cardId);

    // If there's an image, delete it from Cloudinary
    if (card.imageUrl) {
      const publicId = card.imageUrl.split("/").pop()?.split(".")[0];
      if (publicId) {
        await cloudinary.uploader.destroy(publicId);
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting card:", error);
    return NextResponse.json(
      ErrorHandler.createApiErrorResponse(error, "Failed to delete card. Please try again."),
      { status: 500 }
    );
  }
}

// Helper function to handle image upload
async function handleImageUpload(request: NextRequest, cardId: string) {
  try {
    await connectDB();
    const formData = await request.formData();
    const image = formData.get("image") as File;

    if (!image) {
      return NextResponse.json(
        { success: false, error: "No image provided" },
        { status: 400 }
      );
    }

    // Convert File to Buffer
    const buffer = Buffer.from(await image.arrayBuffer());

    // Process image with sharp
    const processedImageBuffer = await sharp(buffer)
      .resize(800, 800, { fit: "inside" })
      .toBuffer();

    // Upload to Cloudinary
    const result = await new Promise<UploadApiResponse>((resolve, reject) => {
      cloudinary.uploader
        .upload_stream(
          {
            resource_type: "image",
            folder: "cards",
          },
          (error, result) => {
            if (error) reject(error);
            if (result) resolve(result);
          }
        )
        .end(processedImageBuffer);
    });

    // Update card with new image URL
    const updatedCard = await Card.findByIdAndUpdate(
      cardId,
      { imageUrl: result.secure_url },
      { new: true }
    );

    if (!updatedCard) {
      return NextResponse.json(
        { success: false, error: "Card not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { imageUrl: updatedCard.imageUrl },
    });
  } catch (error) {
    console.error("Error updating card image:", error);
    return NextResponse.json(
      ErrorHandler.createApiErrorResponse(error, "Failed to update card image. Please try again."),
      { status: 500 }
    );
  }
}

// Helper function to handle card data update
async function handleCardDataUpdate(request: NextRequest, cardId: string) {
  try {
    await connectDB();
    const body = await request.json();
    // Log for development only
    if (process.env.NODE_ENV === 'development') {
      console.log('UPDATE API RECEIVED:', JSON.stringify(body, null, 2));
    }

    // Extract card data fields (excluding userId which is not needed for update)
    const { name, title, company, phone, email, address, website, qrCode, rawText, notes } = body;

    // Update card with new data
    const updateData = {
      name,
      title,
      company,
      phone,
      email,
      address,
      website,
      qrCode,
      rawText,
      notes: notes || "" // Ensure notes is not undefined
    };

    // Log for development only
    if (process.env.NODE_ENV === 'development') {
      console.log('UPDATING CARD WITH:', JSON.stringify(updateData, null, 2));
    }

    const updatedCard = await Card.findByIdAndUpdate(
      cardId,
      updateData,
      { new: true }
    );

    // Log for development only
    if (process.env.NODE_ENV === 'development') {
      console.log('UPDATED CARD:', JSON.stringify(updatedCard, null, 2));
    }

    if (!updatedCard) {
      return NextResponse.json(
        { success: false, error: "Card not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedCard,
    });
  } catch (error) {
    console.error("Error updating card data:", error);
    return NextResponse.json(
      ErrorHandler.createApiErrorResponse(error, "Failed to update card data. Please try again."),
      { status: 500 }
    );
  }
}

// DELETE handler
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ cardId: string }> }
) {
  const { cardId } = await params;
  return deleteCard(cardId);
}
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ cardId: string }> }
) {
  const { cardId } = await params;
  const contentType = request.headers.get("content-type") || "";

  if (contentType.includes("multipart/form-data")) {
    return handleImageUpload(request, cardId);
  } else if (contentType.includes("application/json")) {
    return handleCardDataUpdate(request, cardId);
  } else {
    return NextResponse.json(
      {
        success: false,
        error: `Unsupported content type: ${contentType}.`,
      },
      { status: 415 }
    );
  }
}