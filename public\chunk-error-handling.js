// This script helps recover from chunk loading errors
(function() {
  // Keep track of failed chunks to avoid infinite reload loops
  const failedChunks = {};
  
  // Listen for chunk load errors
  window.addEventListener('error', function(event) {
    const target = event.target;
    
    // Check if this is a script loading error
    if (target && target.tagName === 'SCRIPT' && target.src) {
      const src = target.src;
      
      // Check if this is a chunk loading error
      if (src.includes('/_next/static/chunks/')) {
        // Detected chunk loading error

        // Extract chunk ID from the URL
        const chunkIdMatch = src.match(/\/chunks\/([^\/]+)/);
        if (chunkIdMatch && chunkIdMatch[1]) {
          const chunkId = chunkIdMatch[1];
          
          // Check if we've already tried to reload this chunk
          if (!failedChunks[chunkId]) {
            failedChunks[chunkId] = true;
            
            // Attempting to recover from chunk loading error
            
            // Clear service worker caches that might contain the failed chunk
            if ('serviceWorker' in navigator && 'caches' in window) {
              caches.keys().then(function(cacheNames) {
                return Promise.all(
                  cacheNames.filter(function(cacheName) {
                    return cacheName.includes('app-chunks');
                  }).map(function(cacheName) {
                    return caches.delete(cacheName);
                  })
                );
              }).then(function() {
                // Cleared chunk caches, reloading page
                // Reload the page to try again
                window.location.reload();
              }).catch(function(err) {
                // Failed to clear caches - reload anyway as a fallback
                window.location.reload();
              });
            } else {
              // If service worker API is not available, just reload
              window.location.reload();
            }
          } else {
            // Already attempted to recover from this chunk error, not reloading to avoid loop
          }
        }
      }
    }
  }, true); // Use capture to get the error before it's handled elsewhere
  
  // Also listen for unhandled promise rejections that might be related to chunk loading
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && typeof event.reason.message === 'string' &&
        (event.reason.message.includes('Loading chunk') ||
         event.reason.message.includes('ChunkLoadError'))) {
      // Detected unhandled chunk load error

      // Only reload if we haven't already tried for this specific error
      const errorMsg = event.reason.message;
      if (!failedChunks[errorMsg]) {
        failedChunks[errorMsg] = true;

        // Wait a moment before reloading to avoid immediate reload loops
        setTimeout(function() {
          // Attempting to recover from chunk error
          window.location.reload();
        }, 2000);
      }
    }
  });
})();
