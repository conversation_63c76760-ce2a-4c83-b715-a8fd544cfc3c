// src/components/CardView.tsx
import React, { useState } from "react";
import Image from "next/image";

interface CardData {
  name: string;
  title: string;
  company?: string;
  phone?: string;
  email?: string;
  address?: string;
  website?: string;
  qrCode?: string;
  imageUrl?: string;
  notes?: string;
  createdAt?: string | Date;
  _id?: string;
}

interface CardViewProps {
  data: CardData;
  onBack?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onUpdateImage?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isLoading?: boolean;
}

interface ShareOption {
  name: string;
  icon: string;
  color: string;
  shareUrl: (data: CardData) => string;
}

interface TooltipState {
  visible: boolean;
  text: string;
  position: { x: number; y: number };
}

const CardView: React.FC<CardViewProps> = ({
  data,
  onBack,
  onEdit,
  onDelete,
  onUpdateImage,
  isLoading = false
}) => {
  // Ensure notes field is defined
  const cardData = {
    ...data,
    notes: data.notes || ""
  };

  // Debug - log for development only
  if (process.env.NODE_ENV === 'development') {
    console.log('CARDVIEW DATA:', {
      receivedNotes: data.notes,
      processedNotes: cardData.notes
    });
  }

  // Process the card data to ensure all fields are properly initialized
  // State for tooltip
  const [tooltip, setTooltip] = useState<TooltipState>({
    visible: false,
    text: "",
    position: { x: 0, y: 0 }
  });

  // State for share popup
  const [sharePopupVisible, setSharePopupVisible] = useState(false);

  // State for delete confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Helper function to format contact information text
  const getContactText = (contactData: CardData = cardData) => {
    return `Contact Information:\n\nName: ${contactData.name}\nTitle: ${contactData.title || ''}\n${contactData.company ? `Company: ${contactData.company}\n` : ''}${contactData.phone ? `Phone: ${contactData.phone}\n` : ''}${contactData.email ? `Email: ${contactData.email}\n` : ''}${contactData.website ? `Website: ${contactData.website}\n` : ''}${contactData.address ? `Address: ${contactData.address}\n` : ''}${contactData.notes ? `Notes: ${contactData.notes}` : ''}`;
  };

  // Define share options
  const shareOptions: ShareOption[] = [
    {
      name: "WhatsApp",
      icon: "fa-brands fa-whatsapp",
      color: "bg-green-500",
      shareUrl: (data) => {
        return `https://wa.me/?text=${encodeURIComponent(getContactText(data))}`;
      }
    },
    {
      name: "Telegram",
      icon: "fa-brands fa-telegram",
      color: "bg-blue-500",
      shareUrl: (data) => {
        return `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(getContactText(data))}`;
      }
    },
    {
      name: "Email",
      icon: "fa-regular fa-envelope",
      color: "bg-red-500",
      shareUrl: (data) => {
        const subject = `Contact Information: ${data.name}`;
        return `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(getContactText(data))}`;
      }
    },
    {
      name: "LinkedIn",
      icon: "fa-brands fa-linkedin-in",
      color: "bg-blue-700",
      shareUrl: () => {
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
      }
    },
    {
      name: "Twitter",
      icon: "fa-brands fa-x-twitter",
      color: "bg-blue-400",
      shareUrl: (data) => {
        return `https://twitter.com/intent/tweet?text=${encodeURIComponent(`Check out ${data.name}'s contact information`)}&url=${encodeURIComponent(window.location.href)}`;
      }
    },
    {
      name: "Copy",
      icon: "fa-regular fa-clipboard",
      color: "bg-gray-500",
      shareUrl: () => {
        return "#";
      }
    }
  ];

  // Function to handle share option click
  const handleShareOptionClick = (option: ShareOption, event: React.MouseEvent) => {
    if (option.name === "Copy") {
      copyToClipboard(getContactText(data), event, "Contact information copied!");
    } else {
      window.open(option.shareUrl(data), "_blank");
    }
    setSharePopupVisible(false);
  };

  // Validation functions
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const isValidUrl = (url: string): boolean => {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === "http:" || parsedUrl.protocol === "https:";
    } catch {
      return false;
    }
  };

  const isValidPhone = (phone: string): boolean => {
    // Basic phone validation - can be adjusted based on requirements
    const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
    return phoneRegex.test(phone.replace(/\s/g, ""));
  };

  // Copy to clipboard function
  const copyToClipboard = (text: string, event: React.MouseEvent, tooltipText: string) => {
    event.preventDefault();
    navigator.clipboard.writeText(text)
      .then(() => {
        // Show tooltip
        const rect = (event.target as HTMLElement).getBoundingClientRect();
        setTooltip({
          visible: true,
          text: tooltipText,
          position: {
            x: rect.left + rect.width / 2,
            y: rect.top - 10
          }
        });

        // Hide tooltip after 2 seconds
        setTimeout(() => {
          setTooltip(prev => ({ ...prev, visible: false }));
        }, 2000);
      })
      .catch(err => {
        // Log error for development only
        if (process.env.NODE_ENV === 'development') {
          console.error("Could not copy text: ", err);
        }
      });
  };
  return (
    <div className="relative w-full min-sm:h-screen overflow-x-hidden">
      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="fixed bg-black text-white px-3 py-1 rounded text-sm z-50 transform -translate-x-1/2 opacity-90 transition-opacity duration-300"
          style={{
            left: `${tooltip.position.x}px`,
            top: `${tooltip.position.y}px`,
          }}
          role="status"
          aria-live="polite"
        >
          {tooltip.text}
          <div className="absolute w-2 h-2 bg-black transform rotate-45 -bottom-1 left-1/2 -translate-x-1/2"></div>
        </div>
      )}
      {/* Background image with blur */}
      <div className="absolute inset-0 z-0">
        <div className="relative w-full h-full">
          <Image
            src={data.imageUrl || "/placeholder.jpg"}
            alt={data.name}
            fill
            className="object-cover blur-sm brightness-[0.5]"
            priority
          />
        </div>
      </div>

      {/* Back button */}
      {onBack && (
        <button
          onClick={onBack}
          className="absolute top-4 left-4 z-20 bg-white bg-opacity-90 text-gray-800 px-4 py-2 rounded-full shadow-md hover:bg-opacity-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Return to dashboard"
        >
          ← Back to Dashboard
        </button>
      )}

      <div className="relative z-10 p-6 flex flex-col md:flex-row min-md:justify-center min-md:items-center h-screen gap-6">
        {/* Left side - Image and update button */}
        <div className="md:w-1/3 flex flex-col items-center">
          <div className="relative w-full max-w-[450px] aspect-[3/2] mb-4 ">
            <Image
              src={data.imageUrl || "/placeholder.jpg"}
              alt={data.name}
              fill
              className="object-cover rounded-md shadow-md"
            />

            {/* Update image button overlay */}
            {onUpdateImage && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 focus-within:opacity-100 transition-opacity duration-300 rounded-md">
                <label
                  className="cursor-pointer bg-white text-gray-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 focus-within:ring-2 focus-within:ring-blue-500 transition-colors"
                  tabIndex={0}
                  role="button"
                  aria-label="Update card image"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      document.getElementById('card-image-upload')?.click();
                    }
                  }}
                >
                  Update Image
                  <input
                    id="card-image-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={onUpdateImage}
                    disabled={isLoading}
                    aria-label="Upload new card image"
                  />
                </label>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Card details and action buttons */}
        <div className="md:w-2/3   bg-opacity-90 p-5 rounded-lg min-md:h-screen justify-center max-md:text-center max-md:items-center  flex flex-col">
          <h2 className="text-6xl  max-md:text-4xl font-bold mb-2 uppercase text-white">{data.name}</h2>
          <hr className="text-white w-xs py-3"  />
          <p className="text-white mb-3 font-medium border-1 p-2 self-start max-md:self-center w-auto">{data.title}</p>

          <div className="space-y-4 mb-6">
            {data.company && (
              <p className="text-xl text-black bg-white max-md:m-auto p-2 min-w-[200px] max-w-100">
                <span className="font-medium">Company:</span> {data.company}
              </p>
            )}

            {data.phone && (
              <p className="text-white">
                <span className="font-medium">Phone:</span>{" "}
                {isValidPhone(data.phone) ? (
                  <a
                    href={`tel:${data.phone}`}
                    className="text-blue-300 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-300 rounded"
                    title="Click to call"
                  >
                    {data.phone}
                  </a>
                ) : (
                  <button
                    onClick={(e) => copyToClipboard(data.phone || "", e, "Copied!")}
                    className="text-yellow-300 hover:underline cursor-pointer focus:outline-none focus:ring-2 focus:ring-yellow-300 rounded"
                    title="Invalid phone number – click to copy"
                    aria-label="Copy invalid phone number to clipboard"
                  >
                    {data.phone}
                  </button>
                )}
              </p>
            )}

            {data.email && (
              <p className="text-white">
                <span className="font-medium">Email:</span>{" "}
                {isValidEmail(data.email) ? (
                  <a
                    href={`mailto:${data.email}`}
                    className="text-blue-300 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-300 rounded"
                    title="Click to email"
                  >
                    {data.email}
                  </a>
                ) : (
                  <button
                    onClick={(e) => copyToClipboard(data.email || "", e, "Copied!")}
                    className="text-yellow-300 hover:underline cursor-pointer focus:outline-none focus:ring-2 focus:ring-yellow-300 rounded"
                    title="Invalid email – click to copy"
                    aria-label="Copy invalid email to clipboard"
                  >
                    {data.email}
                  </button>
                )}
              </p>
            )}

            {data.address && (
              <p className="text-white">
                <span className="font-medium">Address:</span>{" "}
                <button
                  onClick={(e) => copyToClipboard(data.address || "", e, "Address copied!")}
                  className="text-blue-300 hover:underline cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-300 rounded"
                  title="Click to copy address"
                  aria-label="Copy address to clipboard"
                >
                  {data.address}
                </button>
              </p>
            )}

            {data.website && (
              <p className="text-white">
                <span className="font-medium">Website:</span>{" "}
                {isValidUrl(data.website) ? (
                  <a
                    href={data.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-300 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-300 rounded"
                    title="Click to visit"
                  >
                    {data.website}
                  </a>
                ) : (
                  <button
                    onClick={(e) => copyToClipboard(data.website || "", e, "Copied!")}
                    className="text-yellow-300 hover:underline cursor-pointer focus:outline-none focus:ring-2 focus:ring-yellow-300 rounded"
                    title="Invalid URL – click to copy"
                    aria-label="Copy invalid URL to clipboard"
                  >
                    {data.website}
                  </button>
                )}
              </p>
            )}

            {data.qrCode && (
              <p className="text-white">
                <span className="font-medium">QR Code:</span>{" "}
                {isValidUrl(data.qrCode) ? (
                  <a
                    href={data.qrCode}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-300 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-300 rounded"
                    title="Click to visit link"
                  >
                    View QR Code
                  </a>
                ) : (
                  <button
                    onClick={(e) => copyToClipboard(data.qrCode || "", e, "QR Code content copied!")}
                    className="text-yellow-300 hover:underline cursor-pointer focus:outline-none focus:ring-2 focus:ring-yellow-300 rounded"
                    title="Invalid QR code – click to copy"
                    aria-label="Copy QR code content to clipboard"
                  >
                    View QR Code Content
                  </button>
                )}
              </p>
            )}

            {/* Notes section */}
            {cardData.notes ? (
              <div className="mt-4 p-3 border-1 border-white bg-opacity-20 rounded-xs self-start w-auto ">
                <p className="text-white">
                  <span className="font-medium">Notes:</span>{" "}
                  <span className="italic">{cardData.notes}</span>
                </p>
              </div>
            ) : (
              <div className="hidden">No notes available</div>
            )}

            {/* Date added */}
            {cardData.createdAt && (
              <p className="text-white max-md:m-auto bg-[#4DA1A9] w-[150px] p-2 self-start max-md:self-center text-sm mt-4">
                <span className="font-medium">Added on:</span>{" "}
                {new Date(cardData.createdAt).toLocaleDateString()}
              </p>
            )}
          </div>

          {/* Action buttons */}
          {(onEdit || onDelete) && (
            <div className="flex gap-4 mt-4 max-w-sm max-md:self-center self-end">
              {/* Share Button */}
              <button
                onClick={() => setSharePopupVisible(true)}
                className="border-2 text-white px-6 py-2 rounded-full hover:bg-[#3c8a92] transition-colors duration-300 flex-1 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center gap-2"
                disabled={isLoading}
                aria-label="Share card details"
              >
                <i className="fa-solid fa-share-nodes"></i>
                <span>Share</span>
              </button>

              {onEdit && (
                <button
                  onClick={onEdit}
                  className="border-2 text-white px-6 py-2 rounded-full hover:bg-[#3c8a92] transition-colors duration-300 flex-1 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center gap-2"
                  disabled={isLoading}
                  aria-label="Edit card details"
                >
                  <i className="fa-solid fa-pen-to-square"></i>
                  <span>{isLoading ? "Processing..." : "Edit"}</span>
                </button>
              )}
              {onDelete && (
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="bg-red-500 border-2 border-black text-black px-6 py-2 rounded-full hover:bg-red-600 transition-colors duration-300 flex-1 focus:outline-none focus:ring-2 focus:ring-red-700 flex items-center justify-center gap-2"
                  disabled={isLoading}
                  aria-label="Delete this card"
                >
                  <i className="fa-solid fa-trash-alt"></i>
                  <span>{isLoading ? "Deleting..." : "Delete"}</span>
                </button>
              )}
            </div>
          )}

          {/* Share Popup */}
          {sharePopupVisible && (
            <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 backdrop-blur-sm">
              <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800">Share Contact</h3>
                  <button
                    onClick={() => setSharePopupVisible(false)}
                    className="text-gray-500 hover:text-gray-700 focus:outline-none h-10 w-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                    aria-label="Close share popup"
                  >
                    <i className="fa-solid fa-times"></i>
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  {shareOptions.map((option) => (
                    <button
                      key={option.name}
                      onClick={(e) => handleShareOptionClick(option, e)}
                      className={`${option.color} text-white p-4 rounded-xl flex flex-col items-center justify-center hover:opacity-90 hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md`}
                      aria-label={`Share via ${option.name}`}
                    >
                      <i className={`${option.icon} text-2xl mb-2`}></i>
                      <span className="text-sm font-medium">{option.name}</span>
                    </button>
                  ))}
                </div>

                <div className="mt-6 text-center text-gray-500 text-sm">
                  <p>Share this contact information with others</p>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Dialog */}
          {showDeleteConfirm && (
            <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 backdrop-blur-sm">
              <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl">
                <div className="flex flex-col items-center mb-6">
                  <div className="bg-red-100 p-3 rounded-full mb-4">
                    <i className="fa-solid fa-exclamation-triangle text-red-500 text-3xl"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 text-center">Delete Card</h3>
                  <p className="text-gray-600 text-center mt-2">Are you sure you want to delete this card? This action cannot be undone.</p>
                </div>

                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 flex-1 flex items-center justify-center gap-2"
                  >
                    <i className="fa-solid fa-times"></i>
                    <span>Cancel</span>
                  </button>
                  <button
                    onClick={() => {
                      setShowDeleteConfirm(false);
                      if (onDelete) onDelete();
                    }}
                    className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 flex-1 flex items-center justify-center gap-2"
                    disabled={isLoading}
                  >
                    <i className="fa-solid fa-trash-alt"></i>
                    <span>{isLoading ? "Deleting..." : "Delete"}</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CardView;
