<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 62.48 110.76">
  <defs>
    <style>
      .cls-1 {
        fill: #211921;
      }

      .cls-2 {
        fill: #4da1a9;
      }

      .cls-3 {
        fill: #ef9a33;
      }

      .cls-4 {
        fill: #848b8b;
        mix-blend-mode: multiply;
        opacity: .4;
      }

      .cls-5 {
        fill: #fff;
      }

      .cls-6 {
        fill: #2e5077;
      }

      .cls-7 {
        fill: #9d8e9c;
      }

      .cls-8 {
        fill: #d5bbc9;
      }

      .cls-9 {
        fill: #66432a;
      }

      .cls-10 {
        fill: #ab9baa;
      }

      .cls-11 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3);
      }

      .cls-12 {
        isolation: isolate;
      }

      .cls-13 {
        fill: #304687;
      }

      .cls-14 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_2);
      }

      .cls-15 {
        fill: #f2a883;
      }

      .cls-16 {
        fill: #3a57a0;
      }

      .cls-17 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_942);
      }

      .cls-18 {
        fill: #472d1a;
      }

      .cls-19 {
        fill: #fbbd9e;
      }

      .cls-20 {
        fill: #191619;
      }

      .cls-21 {
        fill: #f9ad87;
      }

      .cls-22 {
        fill: url(#_Áåçûìÿííûé_ãðàäèåíò_3-2);
      }

      .cls-23 {
        fill: #79d7be;
      }

      .cls-24 {
        fill: #efefed;
      }

      .cls-25 {
        fill: #5c3c25;
      }

      .cls-26 {
        fill: #724a2f;
      }
    </style>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_2" data-name="Áåçûìÿííûé ãðàäèåíò 2" x1="53.43" y1="81.98" x2="54.69" y2="81.98" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#5c3c25"/>
      <stop offset=".28" stop-color="#573823"/>
      <stop offset="1" stop-color="#4f3320"/>
    </linearGradient>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="33.69" y1="78.65" x2="35.53" y2="78.65" gradientTransform="translate(83.96) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#e5cfdd"/>
      <stop offset="1" stop-color="#ecd5e4"/>
    </linearGradient>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_3-2" data-name="Áåçûìÿííûé ãðàäèåíò 3" x1="41.91" y1="73.9" x2="43.75" y2="73.9" xlink:href="#_Áåçûìÿííûé_ãðàäèåíò_3"/>
    <linearGradient id="_Áåçûìÿííûé_ãðàäèåíò_942" data-name="Áåçûìÿííûé ãðàäèåíò 942" x1="47.96" y1="68.82" x2="49.98" y2="38.66" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3a57a0"/>
      <stop offset=".4" stop-color="#38539b"/>
      <stop offset=".83" stop-color="#324a8e"/>
      <stop offset="1" stop-color="#304687"/>
    </linearGradient>
  </defs>
  <g class="cls-12">
    <g id="Layer_2" data-name="Layer 2">
      <g id="_0" data-name="0">
        <g id="_1" data-name="1">
          <g>
            <g>
              <ellipse class="cls-4" cx="8.95" cy="84.04" rx="8.95" ry="5.05"/>
              <path class="cls-4" d="M22.47,70.55c0,2.79-4.01,5.05-8.95,5.05s-8.95-2.26-8.95-5.05,4.01-5.05,8.95-5.05,8.95,2.26,8.95,5.05Z"/>
            </g>
            <g>
              <ellipse class="cls-4" cx="48.14" cy="105.72" rx="8.95" ry="5.05"/>
              <path class="cls-4" d="M62.08,94.43c0,2.17-3.12,3.93-6.97,3.93s-6.97-1.76-6.97-3.93,3.12-3.93,6.97-3.93,6.97,1.76,6.97,3.93Z"/>
            </g>
          </g>
          <g id="_5" data-name="5">
            <g>
              <path class="cls-20" d="M18.57,68.73c-2.67-.62-4.68-.05-5.8-1.7-.71-1.04-3.1-.49-4.32-.09-.53.17-.6.34-.65.66-.19,1.24.11,3.59.24,3.79.17.28,1.53.19,1.86.18.14,0,.29-.1.3-.24,0-.14-.01-.2.16-.17,3.56.56,7.48.59,8.13.49.88-.13,1.41-.84,1.33-1.55-.06-.52-.17-1.13-1.23-1.38Z"/>
              <path class="cls-3" d="M19.8,70.11c-.06.27-.25.55-.54.76-.33.25-.99.35-1.4.33-2.58-.11-9.72-.37-9.83-.45-.05-.04-.08-.25-.12-.52-.03,0-.07,0-.1,0,.07.68.16,1.22.22,1.31.17.28,1.53.19,1.86.18.14,0,7.93.18,8.59.08,1-.04,1.68-.99,1.33-1.7Z"/>
            </g>
            <g>
              <path class="cls-20" d="M3.39,81.63c0,.28.16.54.42.65.12.05.24.1.36.16.36.19.64.5.91.8,1.01,1.09,2.13,2.11,3.43,2.84,3.61,2.02,6.23.7,5.91-1.16-.38-2.23-4.59-3.29-5.28-4.92-.13-.32-.32-1.62-.57-1.84-.27-.24-3.2-.37-4.31.18-.38.19-.84.35-.89.79v2.49Z"/>
              <path class="cls-3" d="M14.43,84.92c-.44,1.39-2.85,2.1-5.91.39-1.3-.73-4.24-3.32-4.58-3.59-.22-.18-.54-.58-.54-.86v1.05c0,.11.04.21.11.29.65.69,3.82,3.36,5.01,4.03,3.61,2.02,6.32.67,5.96-1.18,0-.04-.03-.09-.05-.13Z"/>
            </g>
            <path class="cls-1" d="M7.5,54.19c-.32,2.27-1.25,2.55-1.42,4.85-.17,2.37.56,4.12.56,7.92,0,2.31,6.38,2.31,6.38-.05,0-1.82.64-8.21,1.12-9.78.37-1.23.72-1.65,1.13-2.88,1.54-4.6,2.26-11.07,2.26-11.07-1.01-.55-2.45-.49-3.74-.39-1.47.11-5.88.17-6.18,1.45-.21.9.03,8.93-.12,9.94Z"/>
            <path class="cls-1" d="M3.71,58.94c-.32,3.36-1.25,3.77-1.42,7.16-.17,3.5.56,7.55.56,13.16,0,1.8,6.38,1.73,6.38-.07,0-2.68.8-12.21,1.27-14.52.37-1.82.8-3.63,1.21-5.44,1.54-6.79,1.8-14.32,1.8-14.32-1.01-.82-2.45-.72-3.74-.58-1.47.16-5.88.25-6.18,2.14-.21,1.33.25,10.95.11,12.46Z"/>
            <g>
              <g>
                <g>
                  <path class="cls-15" d="M30.45,35.26c-.02-.06-.07-.1-.12-.13-.27-.17-.59-.22-.91-.25-.47-.05-.95-.05-1.41.03-.37.07-.73.2-1.04.4-.32.21-.46.63-.64.97-.1.18-.19.39-.05.58.08.11.25.17.37.1-.05.17-.02.36.09.5s.3.21.47.19c.18-.02.34-.13.49-.24.15-.11.46-.44.66-.42.24.03-.07.34-.12.41-.11.14-.22.29-.24.47-.02.18.09.38.27.4.17.02.3-.13.4-.26.25-.33.48-.67.72-1.02.33-.49.66-.98,1-1.47.05-.08.11-.18.08-.27Z"/>
                  <path class="cls-19" d="M31.37,37.62c.15-.55.26-1.11.33-1.67.02-.16.04-.32,0-.47-.05-.16-.16-.3-.28-.42-.5-.55-1.04-1.07-1.61-1.54-.13-.11-.27-.21-.42-.28-.14-.06-.29-.09-.43-.12-.52-.11-1.04-.21-1.56-.32-.15-.03-.3-.06-.41-.17-.12-.11-.16-.27-.2-.42-.03-.12-.07-.24-.1-.36-.19-.67-.92-1.02-1.56-.74-.51.22-.99.45-1.46.7.28.6.5,1.22.57,1.88.02.18.03.37.11.54.08.15.21.26.34.37,1.54,1.29,3.36,2.36,5.01,3.52.15.1.32.21.5.17.25-.06.34-.39.24-.62-.1-.23-.31-.39-.52-.55-.2-.15-.41-.33-.47-.58.11-.38.3-.84.69-.88.42-.04.71.46.7.89,0,.43-.19.84-.16,1.27,0,.09.02.19.09.26.11.11.31.08.42-.03.11-.11.16-.26.2-.41Z"/>
                </g>
                <path class="cls-5" d="M21.63,31.51c1.18,1.32,2.28,2.56,2.94,3.4.9,1.11,3.2-1.55,2.75-2.42-.39-.74-1.44-2.12-2.59-3.56-.98.87-2.01,1.77-3.1,2.57Z"/>
                <path class="cls-23" d="M20.3,22.59c-.2-.84-1.28-6.37-1.62-8.33-.45-2.62-1.17-5.13-3.59-5.72-.47-.12-.95-.14-1.38-.13-.92.39-1.17.65-1.29,1.56,1.01,7.07,2.62,13.97,3.21,16.25.01.33,6.19,6.42,8.12,8.07,1.22,1.03,4.07-2.01,3.05-3.33-3.91-5.06-6.39-7.88-6.5-8.35Z"/>
              </g>
              <path class="cls-2" d="M16.43,9.47c-.77-.76-1.93-.91-2.93-.49-6.9,2.96-10.41,7.52-10.9,9.41-.9,3.48,1.63,8.63,1.44,16.02-.12,4.43-1.8,12-1.36,13.12.7,1.8,5.68,2.42,7.74,2.06,2.45-.42,3.82-.33,5.08-5.87.25.84.65,1.63,1.06,1.75.4.13,1.1-.33,1.35-.67.16-.22.2-.5.23-.77.14-1.18.36-11.56.43-13.12.01-1.19.77-18.53-2.15-21.43Z"/>
              <g>
                <path class="cls-23" d="M7.28,11.58c-3.6,3.94,5.97,6.84,10.27,15.71.09-1.93,1.39-9.13-2.91-15.42-.84-1.23-1.49-1.37-2.93-2.16-.78-.43-.33-.85-2.13.07-.57.3-1.87,1.33-2.3,1.8Z"/>
                <path class="cls-24" d="M17.23,22.88c-.32-2.44-.87-4.98-1.58-7.14-.42-1.26-1.12-2.42-2.12-3.3-1.33-1.18-2.69-3.14-3.28-2.8-.46.26-.81.57-1.22.76-.67.32-.96.83-1.08,1.43-.14.72-.03,1.56.68,2.52,3.92,3.07,5.99,6.23,7.36,9.13.63,1.34,1.31.96,1.24-.59Z"/>
                <path class="cls-6" d="M14.96,18.61c.75-.29,1.07-3.22-.51-2.34-.24.13-.78.46-.94.68-.18.25-.24.74-.12,1.03.15.35,1.2.77,1.57.63Z"/>
                <path class="cls-5" d="M14.29,16.25l1.95.68s-1.31-3.32-2.27-4.1c1.79,2.65.32,3.42.32,3.42Z"/>
                <path class="cls-15" d="M14.11,14.63c.21-1.1.34-.64-1.04-3.23-.37-.69-.55-.82-1.21-1.02-1.87-.58-1.88-.11-2.83-.3-.05-.01-.11-.02-.15,0-.04.02-.42-.24-.41-.2.17,1.64-.05,2.43-.07,2.71-.06.91.77,1.45,1.39,2.09.38.39.69.85,1.12,1.18.36.28,1.81.97,2.22.89.36-.07.89.13.95-.32.04-.24-.08-.99.04-1.79Z"/>
                <path class="cls-5" d="M13.3,19.01l.9-2.68s-2.08.3-5.28-3.13c0,0-1-1.17-.62-2.24,0,0-.68,1.01-.72,1.93-.04.88,3.81,4.8,5.71,6.12Z"/>
                <path class="cls-19" d="M8.52,9.29c.28.74.64,1.45,1.07,2.12,1.23,1.88,4.01,3.03,4.81,3.2,1.01.2,2.13.43,2.21-.59.19-2.25,1.13-7.06.41-8.72-1.1-2.51-7.72-1-8.71.03-.34.35-.48,1.12-.5,1.61-.01.32.11.43.23.71.23.53.28,1.12.48,1.66Z"/>
                <path class="cls-26" d="M9.63,8.67c.16.41.25,1.58.36,1.75.09.13.49.29.65.21.16-.08.13-.35.13-.5-.05-1.64.18-1.93.61-2.4,1.31-1.44,5.49-3.31,5.82-1.85.11-.48.08-1.14-.03-1.79-.35-2.1-1.95-3.31-3.99-3.98-1.99-.64-8.75,1.05-5.45,8.95.14.34.38.63.67.86.26.2.51.38.51.38.07-.51-.2-1.04-.37-1.53-.53-1.5.7-1.15,1.1-.12Z"/>
                <path class="cls-19" d="M9.63,8.67c.3.91-.09,1.33.57,2.84-.75.03-.88-.59-1.13-.94-2.36-2.35-.19-4.14.55-1.9Z"/>
                <path class="cls-6" d="M17.19,22.58l-1.94-4.54-.52.36,1.17,4.87c.68,1.41,1.19,2.76,1.65,4.01,0-1.44-.13-3.06-.36-4.7Z"/>
              </g>
              <g>
                <path class="cls-23" d="M10.54,19.96c.68,4.29,1.49,11.92,2.19,13.02s5.47,3.62,8.48,4.93c1.25.63-.72,5.44-2.3,4.65-1.98-.99-10.27-4.75-11.14-5.71-.87-.96-3.75-10.07-4.87-14.83-.39-1.66-.44-2.92.11-3.98.67-1.3,2.18-2.03,3.61-1.76,2.46.47,3.84,3.22,3.91,3.68Z"/>
                <path class="cls-5" d="M21.56,38.9l-.46-.23c-1.01-.45-3.06,3.35-1.79,3.93l.31.16c1.25.63,2.92-3.36,1.93-3.86Z"/>
                <path class="cls-19" d="M23.32,44.82c.44.31.9.38,1.68.47.88.11,1.92-.08,2.22-.21.13-.05.24-.14.36-.2.2-.11.44-.17.64-.28.45-.26.64-.95.25-1.12-.19-.08-1.22-.18-1.83-.24-.3-.03-.59-.11-.86-.24-.67-.32-1.38-.65-1.58-.96-.16-.26-.36-.87.01-1.43.06-.08.2-.16.29-.2.66-.24,1.02-1.09.74-1.73-.03-.06-.06-.12-.12-.16-.1-.07-.25-.05-.35.02-.19.13-.25.32-.39.48-.15.18-.41.27-.63.33-.63.16-1.29.18-1.92.03-.21-.05-.32-.08-.54,0-.55.18-.95.7-1.2,1.36-.41,1.07-.25,1.5.2,1.77.47.29,1.7,1.36,3.03,2.3Z"/>
              </g>
            </g>
          </g>
          <g>
            <path class="cls-20" d="M54.39,106.27c0-.11,0-.23,0-.34.02-1.35.05-2.7.07-4.05-.92-.26-1.86-.44-2.82-.54l-3.07.52c-1,1.54-7.41.71-7.74,3.1-.5,2.11,5.84,2.24,6.81,2.05.71-.14,1.88-.28,2.44-.43,0,0,4.29-.15,4.29-.3Z"/>
            <path class="cls-20" d="M60.28,92.67l-6.53-1.67c-.91.28-1.52,1.92-1.25,2.82.11.36,3.42,1.4,4.45,1.67.88.23,2.02.2,2.85-.22.16-.08.48-.26.48-.48,0,0,0-1.82,0-2.11Z"/>
            <path class="cls-3" d="M58.84,95.45c-.75.12-1.51.06-2.25-.1-.95-.21-2.01-.54-2.65-.91l.29,1.04c.88.36,2.93.82,3.4.82,1.9.11,2.42-.41,2.58-.65.04-.07.06-.15.06-.23v-.72s-.02.53-1.43.75Z"/>
            <path class="cls-3" d="M54.35,105.89c-.25.24-3.28.49-4.01.56-3.6.32-5.52.21-7.05.14-.67-.03-1.34-.21-1.87-.62-.35-.27-.6-.63-.53-1.11-.15.29-.16.65.02,1.08.27.64.86,1.1,1.53,1.25,1.75.39,6.09.11,7.08.02.97-.08,4.49-.31,4.74-.54.11-.1.12-.26.12-.41,0-.16,0-.32,0-.48-.02.03-.02.07-.05.1Z"/>
            <g>
              <path class="cls-21" d="M44.95,42.48c-.26-.64-.18-1.5-1.65-1.5,0,0-1.39-.01-3.04.03-.44.01-1.62.42-1.79.47-1.09.34-2.01.64-1.85,1.04.1.25.5.32.78.24-.73.19-1.27.4-.94.91.11.17.43.13,1.05.02-.79.29-.7.71-.28.84.17.05.57.02,1.04-.05-.41.22-.29.77.56.66.32-.05,1.64-.23,1.64-.23.88-.03,1.26-.05,1.56-.07.99-.06,2.08-.23,2.08-.23.52-.46,1.09-1.47.83-2.12Z"/>
              <path class="cls-19" d="M38.81,41.71c-.11.14-.14.35-.02.47.33.36,1.25.05,1.71-.08.56-.15,1.12-.4,1.65-.04.64.44.91,1.18,1.72,1.11.34-.03.67-.25.75-.58.04-.14.03-.29,0-.44-.1-.79-.34-1.15-1.02-1.22-.22-.02-1.13-.14-1.33-.16-.39-.04-.81,0-1.18.12-.32.1-2.09.54-2.3.81Z"/>
            </g>
            <path class="cls-5" d="M46.83,40.55l-3.02.07c-1.74.17-1.63,4.34-.43,4.54l5.02.03-1.57-4.65Z"/>
            <path class="cls-13" d="M47.79,40.36l-3.34.08c-1.92.19-1.8,4.79-.48,5.02l5.55.04-1.73-5.14Z"/>
            <path class="cls-1" d="M59.63,72.87c-.03-2.56-.02-7.45-.17-10.26l-6,3.78c-.41,2.85-.59,5.72-.54,8.6.04,2.74,1.74,14.03,2.04,17.5.19,1.18,5.36,1.17,5.41.18.53-10.49.32-13.58-.41-16.76-.16-.7-.32-2.33-.33-3.05Z"/>
            <path class="cls-1" d="M54.93,95.8c.16-3.2.31-6.41-.15-9.58-.24-1.65-.56-3.25-.68-4.91-.11-1.59.27-3.07.55-4.65.63-3.51,1.16-7.05,1.33-10.61.04-.78.02-1.65-.53-2.21-.49-.49-1.25-.57-1.94-.62-.87-.06-1.76-.12-2.62.01-2.03.31-3.51,1.5-5.35,2.42,0,0,.22,8.91.74,13.11.49,3.94,1.14,5.92,1.32,8.43.24,3.48.23,9.88.27,14.46.02,2.42,6.56,2.89,6.7.9.15-2.28.25-4.48.36-6.76Z"/>
            <path class="cls-13" d="M61.1,31.51c.64.55,1.1,1.31,1.28,2.24.28,1.46-.07,3.05-.22,4.52-.41,3.93-.45,6.71-1.04,10.24-1.1,6.53,1.29,5.18.37,14.8-.23,2.44-1.77,1.55-1.77,1.55,0,0-.24.68-.72,1.33-2.57,3.44-7.4,2.85-11.17,2.71-.56-.02-2.16-.4-2.62-.73-.11-.08.62-3.99.71-5.08.17-2.04.25-4.09.29-6.13.08-4.03,0-8.05.05-12.08.03-1.89.09-3.81.57-5.64.48-1.83,1.41-3.6,2.91-4.76.31-.24.65-.46.99-.65.46-.26,2.53-.23,3.01-.46,1.51-.73,1.69-.71,2.97-1.6.38-.19.15-1.02.57-1.1,1.54-.28,2.99.11,3.83.84Z"/>
            <g>
              <g id="Axonometric_Cube" data-name="Axonometric Cube">
                <path class="cls-25" d="M53.23,87.76l.21-.12c.3-.75.69-.79,1.25-.72l.14-.08v-11.46s-5.91-1.56-5.91-1.56l4.31,13.94Z"/>
                <path class="cls-14" d="M53.44,77.05v10.6c.3-.75.69-.79,1.25-.72v-10.62s-1.25.74-1.25.74Z"/>
                <polygon class="cls-18" points="52.85 82.77 52.84 76.54 52.84 75.7 37.88 67.9 37.88 74.12 38.27 74.35 38.27 79.13 53.23 87.76 53.23 82.55 52.85 82.77"/>
                <polygon id="Cube_face_-_top" data-name="Cube face - top" class="cls-9" points="52.84 76.54 54.83 75.38 39.88 66.75 37.88 67.9 52.84 76.54"/>
                <polygon class="cls-18" points="54.69 75.7 53.01 76.67 53.01 77.15 53.01 77.3 53.01 82.76 53.22 82.64 53.22 77.17 54.68 76.31 54.69 75.7"/>
                <g>
                  <g id="Axonometric_Cube-2" data-name="Axonometric Cube">
                    <polygon id="Cube_face_-_left" data-name="Cube face - left" class="cls-8" points="50.06 80.51 50.27 80.39 50.27 79.06 50.06 79.18 50.06 80.51"/>
                    <polygon id="Cube_face_-_right" data-name="Cube face - right" class="cls-10" points="48.43 79.57 50.06 80.51 50.06 79.18 48.43 78.24 48.43 79.57"/>
                    <polygon id="Cube_face_-_top-2" data-name="Cube face - top" class="cls-11" points="50.06 79.18 50.27 79.06 48.64 78.12 48.43 78.24 50.06 79.18"/>
                  </g>
                  <g id="Axonometric_Cube-3" data-name="Axonometric Cube">
                    <polygon id="Cube_face_-_left-2" data-name="Cube face - left" class="cls-7" points="41.83 75.77 42.04 75.64 42.04 74.31 41.83 74.43 41.83 75.77"/>
                    <polygon id="Cube_face_-_right-2" data-name="Cube face - right" class="cls-10" points="40.21 74.83 41.83 75.77 41.83 74.43 40.21 73.49 40.21 74.83"/>
                    <polygon id="Cube_face_-_top-3" data-name="Cube face - top" class="cls-22" points="41.83 74.43 42.04 74.31 40.42 73.37 40.21 73.49 41.83 74.43"/>
                  </g>
                </g>
              </g>
              <path class="cls-18" d="M46.16,69.53c-.5.96-1.81.85-1.81.85,0,0-.69.43-1.52-.08-.46-.28-.32-.65-.19-.79,1.07-1.16,3.98-.9,3.51.02Z"/>
            </g>
            <path class="cls-19" d="M48.43,67.15c-.23-.38-1-.75-1.38-.81-.68-.12-2.05.45-2.32,1.14-.17.43-.27.81-.48,1.23-.27.56-.58,1.31-.53,1.94.03.38.13.48.4.65.26.16.35.07.61.22.1.06.64.54.75.57.65.22.76.56,1.45.57.54,0,.95-.32,1.3-.73.22-.27.45-.55.6-.86.31-.67.1-1.53-.02-2.22-.09-.5-.11-1.24-.39-1.69Z"/>
            <path class="cls-18" d="M47.37,71.82c.71.82,1.24,1.83,1.24,1.83,0,0,.6.59,1.42.08.46-.28.27-.61.19-.79-.26-.55-.85-1.49-1.42-2.06-.43-.43-1.77.56-1.43.94Z"/>
            <path class="cls-5" d="M44.77,66.01c-.08.81-.14,1.37-.15,1.51-.2,1.6,3.9,1.92,4.13,1.34.2-.5.47-1.34.75-2.38-1.59-.1-3.18-.17-4.73-.47Z"/>
            <path class="cls-19" d="M56.8,28.16l-5.71-3.04s-3.58-3.57-3.67-2.59c-.09.98.18,7.68.71,8.4.21.29,1.72.54,2.35.48l.23,1.44c3.61.06,6.34-1.38,6.21-2.42-.13-1.05-.13-2.26-.13-2.26Z"/>
            <path class="cls-5" d="M50.07,34.23s.48-1.51.48-1.51c1.63-.05,3.17-.19,4.71-.91.93-.49,1.62-1.05,1.64-1.6l.57.84s1.19.8-2.32,2.49c-1.47.71-3.44.72-5.07.7Z"/>
            <path class="cls-16" d="M50.25,33.69c1.05-.01,2.1-.14,3.12-.39,2.44-.6,2.78-.92,3.14-1.17,1.18-.82.68-1.41.61-1.6,2.31-.24,1.27,2.26-.59,3.12-3.45,1.61-7.09,1.16-7.01,1,.1-.2.51-.8.73-.96Z"/>
            <path class="cls-26" d="M51.83,17.43c1.97-.08,3.34.39,4.3,1.09,1.22.89,1.88,2.36,1.91,3.87.04,2.15-.21,5.05-1.21,6.7-.64,1.41-5.71,2.33-5.85-.74.09-.96.08-1.81-.2-2.42-.28-.59-1.1-.81-1.34-.08-.04.13-.15.61-.21.74-.14.26-.44.17-.46-.26-.03-.44-.01-1.12-.05-1.55-.07-.9-1.01-1.99-1.33-2.12-.16-3.8,1.24-5.1,4.45-5.23Z"/>
            <path class="cls-17" d="M45.68,53.83c-.16,2.52-1.08,12.23-1.17,12.89-.23,1.81,4.4,2.17,4.67,1.51.88-2.18,2.83-10.2,3.22-14.38.79-8.36,1.1-10.86,1.2-12.76.1-1.88-.75-3.74-2.33-4.76-2.29-1.48-5.41-.79-5.46,9.3-.01,2.14-.06,7.22-.13,8.2Z"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>