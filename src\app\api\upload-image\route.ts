import { NextRequest, NextResponse } from "next/server";
import sharp from "sharp";
import { v2 as cloudinary, UploadApiResponse } from "cloudinary";

// Configure Cloudinary (ensure these environment variables are set in .env.local)
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME!,
  api_key: process.env.CLOUDINARY_API_KEY!,
  api_secret: process.env.CLOUDINARY_API_SECRET!,
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("image") as File;
    if (!file) {
      return NextResponse.json(
        { success: false, error: "No image file provided" },
        { status: 400 }
      );
    }

    // Read file as Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Resize image with Sharp: max width 1920, max height 1080, using 'inside' to keep aspect ratio
    const processedBuffer = await sharp(buffer)
      .resize({ width: 1920, height: 1080, fit: "inside" })
      .toFormat("webp", { quality: 80 })
      .toBuffer();

    // Upload processed image to Cloudinary using an upload stream
    const uploadResult = await new Promise<UploadApiResponse>((resolve, reject) => {
      const stream = cloudinary.uploader.upload_stream(
        { folder: "cards" },
        (error, result) => {
          if (error) return reject(error);
          resolve(result as UploadApiResponse);
        }
      );
      stream.end(processedBuffer);
    });

    return NextResponse.json({ success: true, imageUrl: uploadResult.secure_url });
  } catch (error) {
    console.error("Upload image error:", error);
    return NextResponse.json(
      { success: false, error: "Image processing failed" },
      { status: 500 }
    );
  }
}
