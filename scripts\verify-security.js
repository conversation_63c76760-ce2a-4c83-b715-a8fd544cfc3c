#!/usr/bin/env node

/**
 * Security Verification Script
 * Checks for potential technology stack exposures in the codebase
 */

const fs = require('fs');
const path = require('path');

// Technology terms to look for
const TECH_TERMS = [
  'next.js', 'nextjs', 'react', 'mongodb', 'mongoose', 'gradio', 
  'clerk', 'vercel', 'cloudinary', 'console.log', 'console.error',
  'console.warn', 'console.info', 'console.debug'
];

// Files to check
const FILES_TO_CHECK = [
  'src/app/api/**/*.ts',
  'src/components/**/*.tsx',
  'src/lib/**/*.ts',
  'public/**/*.js',
  'src/app/**/*.tsx'
];

// Files to exclude from checks
const EXCLUDE_FILES = [
  'node_modules',
  '.next',
  '.git',
  'scripts/verify-security.js',
  'TECHNOLOGY_EXPOSURE_AUDIT_REPORT.md',
  'PRODUCTION_SECURITY_CHECKLIST.md'
];

function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    
    // Skip excluded directories
    if (EXCLUDE_FILES.some(exclude => fullPath.includes(exclude))) {
      return;
    }

    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else if (fullPath.match(/\.(ts|tsx|js|jsx)$/)) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function checkFileForExposures(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];

  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    const lowerLine = line.toLowerCase();

    // Check for console statements (except in development-only blocks)
    if (line.includes('console.') && !line.includes('NODE_ENV === \'development\'')) {
      // Allow console.error for server-side logging
      if (!line.includes('console.error') || filePath.includes('public/')) {
        issues.push({
          line: lineNumber,
          content: line.trim(),
          type: 'console_statement',
          severity: 'medium'
        });
      }
    }

    // Check for technology terms in user-facing strings
    TECH_TERMS.forEach(term => {
      if (lowerLine.includes(term.toLowerCase()) && 
          (line.includes('"') || line.includes("'")) &&
          !line.includes('//') && // Skip comments
          !line.includes('import') && // Skip imports
          !line.includes('from')) { // Skip imports
        
        issues.push({
          line: lineNumber,
          content: line.trim(),
          type: 'tech_exposure',
          term: term,
          severity: 'high'
        });
      }
    });

    // Check for specific error message patterns
    if (line.includes('error') && 
        (lowerLine.includes('failed to connect') || 
         lowerLine.includes('gradio') ||
         lowerLine.includes('mongodb') ||
         lowerLine.includes('next.js'))) {
      issues.push({
        line: lineNumber,
        content: line.trim(),
        type: 'error_exposure',
        severity: 'high'
      });
    }
  });

  return issues;
}

function runSecurityCheck() {
  console.log('🔍 Running Security Verification Check...\n');

  const allFiles = getAllFiles('./src').concat(getAllFiles('./public'));
  let totalIssues = 0;
  let highSeverityIssues = 0;

  allFiles.forEach(filePath => {
    const issues = checkFileForExposures(filePath);
    
    if (issues.length > 0) {
      console.log(`⚠️  Issues found in: ${filePath}`);
      
      issues.forEach(issue => {
        const severity = issue.severity === 'high' ? '🚨' : '⚠️';
        console.log(`   ${severity} Line ${issue.line}: ${issue.type}`);
        console.log(`      ${issue.content}`);
        
        if (issue.term) {
          console.log(`      Exposed term: "${issue.term}"`);
        }
        
        totalIssues++;
        if (issue.severity === 'high') {
          highSeverityIssues++;
        }
      });
      
      console.log('');
    }
  });

  // Summary
  console.log('📊 Security Check Summary:');
  console.log(`   Total files checked: ${allFiles.length}`);
  console.log(`   Total issues found: ${totalIssues}`);
  console.log(`   High severity issues: ${highSeverityIssues}`);
  console.log(`   Medium severity issues: ${totalIssues - highSeverityIssues}`);

  if (totalIssues === 0) {
    console.log('\n✅ No security issues found! Your application is ready for production.');
  } else if (highSeverityIssues === 0) {
    console.log('\n⚠️  Only minor issues found. Consider reviewing medium severity items.');
  } else {
    console.log('\n🚨 High severity issues found! Please address these before production deployment.');
  }

  return totalIssues;
}

// Run the check
if (require.main === module) {
  const issueCount = runSecurityCheck();
  process.exit(issueCount > 0 ? 1 : 0);
}

module.exports = { runSecurityCheck };
