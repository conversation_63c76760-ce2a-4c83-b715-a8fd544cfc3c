// scripts/copy-favicon.js
const fs = require('fs');
const path = require('path');

// This script copies the favicon.ico file to the root directory

function copyFavicon() {
  try {
    // Source favicon file
    const sourceFavicon = path.join(process.cwd(), 'public', 'favicon.ico');
    
    // Destination path
    const destFavicon = path.join(process.cwd(), '.next', 'favicon.ico');
    
    // Copy the file
    fs.copyFileSync(sourceFavicon, destFavicon);
    
    console.log('✅ Successfully copied favicon.ico to .next directory');
  } catch (error) {
    console.error('❌ Error copying favicon.ico:', error);
  }
}

// Execute the function
copyFavicon();
