const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Create directories if they don't exist
const iconsDir = path.join(process.cwd(), 'public', 'icons');
const screenshotsDir = path.join(process.cwd(), 'public', 'screenshots');

if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

// Source icon paths
const sourceIconPath = path.join(process.cwd(), 'public', 'icons', 'icon512_rounded.png');
const sourceMaskableIconPath = path.join(process.cwd(), 'public', 'icons', 'icon512_maskable.png');

// Icon sizes to generate
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Generate regular icons
async function generateIcons() {
  try {
    // Check if source icon exists
    if (!fs.existsSync(sourceIconPath)) {
      console.error(`Source icon not found: ${sourceIconPath}`);
      return;
    }

    // Generate regular icons
    for (const size of sizes) {
      const outputPath = path.join(iconsDir, `icon-${size}x${size}.png`);
      await sharp(sourceIconPath)
        .resize(size, size)
        .toFile(outputPath);
      console.log(`Generated: ${outputPath}`);
    }

    // Generate maskable icon
    if (fs.existsSync(sourceMaskableIconPath)) {
      const outputPath = path.join(iconsDir, 'icon-512x512-maskable.png');
      await sharp(sourceMaskableIconPath)
        .resize(512, 512)
        .toFile(outputPath);
      console.log(`Generated: ${outputPath}`);
    } else {
      console.warn(`Maskable icon source not found: ${sourceMaskableIconPath}`);
    }

    // Generate scan shortcut icon
    const scanShortcutPath = path.join(iconsDir, 'scan-shortcut.png');
    await sharp(sourceIconPath)
      .resize(192, 192)
      .toFile(scanShortcutPath);
    console.log(`Generated: ${scanShortcutPath}`);

    // Create placeholder screenshots if they don't exist
    const dashboardScreenshot = path.join(screenshotsDir, 'dashboard.png');
    const scannerScreenshot = path.join(screenshotsDir, 'scanner.png');

    if (!fs.existsSync(dashboardScreenshot)) {
      // Create a simple placeholder
      await sharp({
        create: {
          width: 1280,
          height: 720,
          channels: 4,
          background: { r: 77, g: 161, b: 169, alpha: 1 } // #4DA1A9
        }
      })
        .composite([
          {
            input: Buffer.from('<svg width="1280" height="720"><text x="640" y="360" font-family="Arial" font-size="48" text-anchor="middle" fill="white">Dashboard Screenshot</text></svg>'),
            top: 0,
            left: 0
          }
        ])
        .png()
        .toFile(dashboardScreenshot);
      console.log(`Generated placeholder: ${dashboardScreenshot}`);
    }

    if (!fs.existsSync(scannerScreenshot)) {
      // Create a simple placeholder
      await sharp({
        create: {
          width: 1280,
          height: 720,
          channels: 4,
          background: { r: 77, g: 161, b: 169, alpha: 1 } // #4DA1A9
        }
      })
        .composite([
          {
            input: Buffer.from('<svg width="1280" height="720"><text x="640" y="360" font-family="Arial" font-size="48" text-anchor="middle" fill="white">Scanner Screenshot</text></svg>'),
            top: 0,
            left: 0
          }
        ])
        .png()
        .toFile(scannerScreenshot);
      console.log(`Generated placeholder: ${scannerScreenshot}`);
    }

    console.log('All PWA icons and assets generated successfully!');
  } catch (error) {
    console.error('Error generating icons:', error);
  }
}

generateIcons();
