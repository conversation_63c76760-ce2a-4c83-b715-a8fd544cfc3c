// src/app/api/cards/route.ts
import { NextRequest, NextResponse } from 'next/server';
import connectDB  from '@/lib/mongodb';
import { Card } from '@/models/Card';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");
    if (!userId) {
      return NextResponse.json({ success: false, error: "Missing userId" }, { status: 400 });
    }

    // Get search parameters
    const searchTerm = searchParams.get("search") || "";
    const startDate = searchParams.get("startDate") || "";
    const endDate = searchParams.get("endDate") || "";

    await connectDB();

    // Build query
    interface MongoDateRange {
      $gte?: Date;
      $lt?: Date;
    }
    
    interface CardQuery {
      userId: string;
      $or?: Array<{ [key: string]: { $regex: string; $options: string } }>;
      createdAt?: MongoDateRange;
    }
    
    const query: CardQuery = { userId };

    // Add search term filter if provided
    if (searchTerm) {
      query.$or = [
        { name: { $regex: searchTerm, $options: 'i' } },
        { company: { $regex: searchTerm, $options: 'i' } },
        { notes: { $regex: searchTerm, $options: 'i' } }
      ];
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        query.createdAt.$lt = endDateObj;
      }
    }

    const cards = await Card.find(query).sort({ createdAt: -1 });
    return NextResponse.json({ success: true, data: cards });
  } catch (error) {
    console.error("Fetch Cards API Error:", error);
    return NextResponse.json({ success: false, error: "Failed to fetch cards" }, { status: 500 });
  }
}
