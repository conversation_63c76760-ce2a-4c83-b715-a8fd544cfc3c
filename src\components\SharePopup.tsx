// src/components/SharePopup.tsx
import React from "react";

interface CardData {
  name?: string;
  title?: string;
  company?: string;
  phone?: string;
  email?: string;
  address?: string;
  website?: string;
  qrCode?: string;
  imageUrl?: string;
  _id?: string;
  [key: string]: unknown;
}

interface ShareOption {
  name: string;
  icon: string;
  color: string;
  shareUrl: (data: CardData) => string;
}

interface SharePopupProps {
  data: CardData;
  onClose: () => void;
  onCopy: (text: string, event: React.MouseEvent, tooltipText: string) => void;
}

const SharePopup: React.FC<SharePopupProps> = ({ data, onClose, onCopy }) => {
  // Helper function to format contact information text
  const getContactText = (data: CardData) => {
    return `Contact Information:\n\nName: ${data.name || 'N/A'}\nTitle: ${data.title || 'N/A'}\n${data.company ? `Company: ${data.company}\n` : ''}${data.phone ? `Phone: ${data.phone}\n` : ''}${data.email ? `Email: ${data.email}\n` : ''}${data.website ? `Website: ${data.website}\n` : ''}${data.address ? `Address: ${data.address}` : ''}`;
  };

  // Define share options
  const shareOptions: ShareOption[] = [
    {
      name: "WhatsApp",
      icon: "fa-brands fa-whatsapp",
      color: "bg-green-500",
      shareUrl: (data) => {
        return `https://wa.me/?text=${encodeURIComponent(getContactText(data))}`;
      }
    },
    {
      name: "Telegram",
      icon: "fa-brands fa-telegram",
      color: "bg-blue-500",
      shareUrl: (data) => {
        return `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(getContactText(data))}`;
      }
    },
    {
      name: "Email",
      icon: "fa-regular fa-envelope",
      color: "bg-red-500",
      shareUrl: (data) => {
        const subject = `Contact Information: ${data.name || 'Business Card'}`;
        return `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(getContactText(data))}`;
      }
    },
    {
      name: "LinkedIn",
      icon: "fa-brands fa-linkedin-in",
      color: "bg-blue-700",
      shareUrl: () => {
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
      }
    },
    {
      name: "Twitter",
      icon: "fa-brands fa-x-twitter",
      color: "bg-blue-400",
      shareUrl: (data) => {
        return `https://twitter.com/intent/tweet?text=${encodeURIComponent(`Check out ${data.name || 'this'}'s contact information`)}&url=${encodeURIComponent(window.location.href)}`;
      }
    },
    {
      name: "Copy",
      icon: "fa-regular fa-clipboard",
      color: "bg-gray-500",
      shareUrl: () => {
        return "#";
      }
    }
  ];

  // Function to handle share option click
  const handleShareOptionClick = (option: ShareOption, event: React.MouseEvent) => {
    if (option.name === "Copy") {
      onCopy(getContactText(data), event, "Contact information copied!");
    } else {
      window.open(option.shareUrl(data), "_blank");
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold text-gray-800">Share Contact</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none h-10 w-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            aria-label="Close share popup"
          >
            <i className="fa-solid fa-times"></i>
          </button>
        </div>

        <div className="grid grid-cols-3 gap-4">
          {shareOptions.map((option) => (
            <button
              key={option.name}
              onClick={(e) => handleShareOptionClick(option, e)}
              className={`${option.color} text-white p-4 rounded-xl flex flex-col items-center justify-center hover:opacity-90 hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md`}
              aria-label={`Share via ${option.name}`}
            >
              <i className={`${option.icon} text-2xl mb-2`}></i>
              <span className="text-sm font-medium">{option.name}</span>
            </button>
          ))}
        </div>

        <div className="mt-6 text-center text-gray-500 text-sm">
          <p>Share this contact information with others</p>
        </div>
      </div>
    </div>
  );
};

export default SharePopup;
