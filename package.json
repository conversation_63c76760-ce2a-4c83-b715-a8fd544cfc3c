{"name": "snapcard", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development node server-simple.js", "dev:next": "cross-env NODE_ENV=development next dev", "build": "cross-env NEXT_DISABLE_ESLINT=1 next build && npm run copy-manifest && npm run copy-fonts && npm run copy-favicon", "build:prod": "npm run build", "start": "npm run build:prod && next start -p 3000", "start:custom": "npm run build:prod && cross-env NODE_ENV=production HTTPS=true node server.js", "start:next": "cross-env NODE_ENV=production HTTPS=true next start", "lint": "next lint", "copy-manifest": "node scripts/copy-manifest.js", "copy-fonts": "node scripts/copy-fonts.js", "copy-favicon": "node scripts/copy-favicon.js", "security-check": "node scripts/verify-security.js", "pre-deploy": "npm run security-check && npm run build"}, "dependencies": {"@clerk/nextjs": "^6.12.5", "@gradio/client": "^1.13.1", "@tailwindcss/forms": "^0.5.10", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "@yudiel/react-qr-scanner": "^2.2.1", "autoprefixer": "^10.4.21", "cloudinary": "^2.6.0", "jsqr": "^1.4.0", "mongoose": "^8.12.1", "next": "15.2.3", "next-pwa": "^5.6.0", "postcss": "^8.5.3", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-webcam": "^7.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.3", "selfsigned": "^2.4.1", "sharp": "^0.34.1", "tailwindcss": "^4.0.14", "typescript": "^5"}}