// src/app/layout.tsx
import "./globals.css";
import { Metadata } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import Navbar from "@/components/Navbar";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/react";

export const metadata: Metadata = {
  title: "KROTI OCR",
  description: "An OCR card scanner life saver",
  icons: {
    icon: "/icons/icon512_maskable.png",
    shortcut: "/icons/icon512_maskable.png",
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "KROTI",
  },
};

import { <PERSON>eist, <PERSON>eist_Mon<PERSON> } from "next/font/google";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider
      appearance={{
        elements: {
          formButtonPrimary: "bg-[#4DA1A9] hover:bg-[#3C8A92]",
          footerActionLink: "text-[#4DA1A9] hover:text-[#3C8A92]",
          card: "shadow-xl rounded-xl border-0",
        },
        variables: {
          colorPrimary: "#4DA1A9",
          colorTextOnPrimaryBackground: "white",
        },
      }}
    >
      <html lang="en">
        <head>
          <meta name="apple-mobile-web-app-capable" content="yes" />
          <meta name="apple-mobile-web-app-status-bar-style" content="default" />
          <meta name="apple-mobile-web-app-title" content="KROTI" />
          <meta name="mobile-web-app-capable" content="yes" />
          <meta name="theme-color" content="#4DA1A9" />
          <meta name="application-name" content="KROTI" />
          <meta name="format-detection" content="telephone=no" />
          <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
          <meta name="description" content="Scan, save, and organize your business card in one place" />

          <link rel="manifest" href="/manifest.json" />
          <link rel="icon" href="/favicon.ico" />
          <link rel="icon" href="/icons/icon-512x512.png" />
          <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
          <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
          <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
          <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png" />

          {/* Font Awesome */}
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
                integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
                crossOrigin="anonymous" referrerPolicy="no-referrer" />
        </head>
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased overflow-x-hidden`}>
          <Navbar />
          {children}
          {/* Only load analytics in production */}
          {process.env.NODE_ENV === 'production' && (
            <>
              <SpeedInsights />
              <Analytics />
            </>
          )}
          <script src="/chunk-error-handling.js" defer />
          <script src="/script.js" defer />
          <script src="/check-fontawesome.js" defer />
        </body>
      </html>
    </ClerkProvider>
  );
}
