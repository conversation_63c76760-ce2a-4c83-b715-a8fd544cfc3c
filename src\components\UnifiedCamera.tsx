// src/components/UnifiedCamera.tsx
"use client";
import React, { useRef, useState, useCallback, useEffect } from 'react';
import Webcam from 'react-webcam';

interface UnifiedCameraProps {
  onCapture: (imageData: string) => Promise<void>;
  disabled?: boolean;
}

const UnifiedCamera: React.FC<UnifiedCameraProps> = ({ onCapture, disabled }) => {
  const webcamRef = useRef<Webcam>(null);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [cameraPermission, setCameraPermission] = useState<boolean | null>(null);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || (window as Window & typeof globalThis & { opera?: string }).opera;
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test((userAgent || '').toLowerCase());
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Check camera permissions
  useEffect(() => {
    const checkCameraPermission = async () => {
      try {
        // Check if mediaDevices is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          setCameraError("Camera API not supported in this browser. Try using Chrome, Firefox, or Safari.");
          setCameraPermission(false);
          return;
        }

        // Check if we're on HTTPS (required for camera access in most browsers)
        if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
          setCameraError("Camera access requires HTTPS. Please use a secure connection.");
          setCameraPermission(false);
          return;
        }

        // Check for available devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const hasCamera = devices.some(device => device.kind === 'videoinput');

        if (!hasCamera) {
          setCameraError("No camera detected on this device");
          setCameraPermission(false);
          return;
        }

        // Try to access the camera with more specific constraints
        try {
          // First try with environment camera (back camera on mobile)
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: 'environment',
              width: { ideal: 1280 },
              height: { ideal: 720 }
            }
          });
          stream.getTracks().forEach(track => track.stop());
          setCameraPermission(true);
        } catch (_unused) {
          // We need to catch the error but don't need to use it
          // Specific camera constraints failed, trying generic constraints
          // If that fails, try with any camera
          const stream = await navigator.mediaDevices.getUserMedia({ video: true });
          stream.getTracks().forEach(track => track.stop());
          setCameraPermission(true);
        }
      } catch (error) {
        // Camera permission error
        setCameraPermission(false);

        // Provide more specific error messages
        if (error instanceof DOMException) {
          if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            setCameraError("Camera access denied. Please enable camera permissions in your browser settings.");
          } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
            setCameraError("No camera found on this device.");
          } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
            setCameraError("Camera is already in use by another application.");
          } else if (error.name === 'OverconstrainedError') {
            setCameraError("Camera doesn't meet the required constraints.");
          } else {
            setCameraError(`Camera error: ${error.message || error.name}`);
          }
        } else {
          setCameraError(`Camera error: ${(error as Error).message}`);
        }
      }
    };

    checkCameraPermission();
  }, []);

  // More flexible video constraints
  const videoConstraints = {
    facingMode: 'environment',
    width: { min: 640, ideal: 1280, max: 1920 },
    height: { min: 480, ideal: 720, max: 1080 }
  };

  const capturePhoto = useCallback(async () => {
    const imageSrc = webcamRef.current?.getScreenshot();
    if (imageSrc) {
      // Exit fullscreen mode if active
      if (isFullscreen && document.exitFullscreen) {
        try {
          await document.exitFullscreen();
          setIsFullscreen(false);
        } catch (err) {
          // Error exiting fullscreen
        }
      }

      // Process the captured image
      onCapture(imageSrc);
    }
  }, [onCapture, isFullscreen]);

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      // Try to detect if we're on a mobile device
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Get the camera container element
      const cameraContainer = document.querySelector('.camera-container') as HTMLElement;

      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen()
          .then(() => {
            setIsFullscreen(true);

            // For mobile devices, try to force landscape orientation
            if (isMobileDevice) {
              try {
                // Lock to landscape orientation if supported
                if (screen.orientation && 'lock' in screen.orientation) {
                  (screen.orientation as any).lock('landscape')
                    .catch((err: any) => {
                      // Could not lock screen orientation
                    });
                }

                // Add a class to handle rotation in CSS
                if (cameraContainer) {
                  cameraContainer.classList.add('fullscreen-landscape');
                }
              } catch (err) {
                // Error setting orientation
              }
            }
          })
          .catch(err => {
            // Error entering fullscreen
          });
      } else if (document.documentElement && 'webkitRequestFullscreen' in document.documentElement) {
        const element = document.documentElement as HTMLElement & { webkitRequestFullscreen: () => void };
        element.webkitRequestFullscreen();
        setIsFullscreen(true);

        // For mobile devices, try to force landscape orientation
        if (isMobileDevice && screen.orientation && 'lock' in screen.orientation) {
          (screen.orientation as any).lock('landscape')
            .catch((err: any) => {
              // Could not lock screen orientation
            });

          // Add a class to handle rotation in CSS
          if (cameraContainer) {
            cameraContainer.classList.add('fullscreen-landscape');
          }
        }
      } else if (document.documentElement && 'msRequestFullscreen' in document.documentElement) {
        const element = document.documentElement as HTMLElement & { msRequestFullscreen: () => void };
        element.msRequestFullscreen();
        setIsFullscreen(true);
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
          .then(() => {
            setIsFullscreen(false);

            // Unlock orientation when exiting fullscreen
            if (screen.orientation && 'unlock' in screen.orientation) {
              (screen.orientation as any).unlock();
            }

            // Remove the landscape class
            const cameraContainer = document.querySelector('.camera-container') as HTMLElement;
            if (cameraContainer) {
              cameraContainer.classList.remove('fullscreen-landscape');
            }
          })
          .catch(err => {
            // Error exiting fullscreen
          });
      } else if (document && 'webkitExitFullscreen' in document) {
        const doc = document as Document & { webkitExitFullscreen: () => void };
        doc.webkitExitFullscreen();
        setIsFullscreen(false);

        // Unlock orientation when exiting fullscreen
        if (screen.orientation && 'unlock' in screen.orientation) {
          (screen.orientation as any).unlock();
        }

        // Remove the landscape class
        const cameraContainer = document.querySelector('.camera-container') as HTMLElement;
        if (cameraContainer) {
          cameraContainer.classList.remove('fullscreen-landscape');
        }
      } else if (document && 'msExitFullscreen' in document) {
        const doc = document as Document & { msExitFullscreen: () => void };
        doc.msExitFullscreen();
        setIsFullscreen(false);
      }
    }
  };

  // Render a permission request UI if permission is denied
  if (cameraPermission === false) {
    return (
      <div className="border-2 border-gray-200 rounded-lg overflow-hidden bg-white">
        <div className="p-6 text-center">
          <div className="bg-red-100 p-4 rounded-full inline-block mb-4">
            <i className="fa-solid fa-camera-slash text-red-500 text-3xl"></i>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Camera Access Required</h3>
          <p className="text-gray-600 mb-4">{cameraError || "Please enable camera access to scan business cards."}</p>

          {/* Troubleshooting tips */}
          <div className="bg-blue-50 p-4 rounded-lg mb-4 text-left">
            <h4 className="font-medium text-blue-800 mb-2">Troubleshooting Tips:</h4>
            <ul className="text-sm text-blue-700 list-disc pl-5 space-y-1">
              <li>Make sure your camera is not being used by another application</li>
              <li>Check that you&apos;ve granted camera permissions in your browser settings</li>
              <li>Try using a different browser (Chrome or Firefox recommended)</li>
              <li>On mobile, make sure you&apos;re using HTTPS</li>
              <li>If on iOS, try using Safari browser</li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition flex items-center justify-center gap-2"
            >
              <i className="fa-solid fa-rotate-right"></i>
              Try Again
            </button>

            {/* Alternative option to upload image */}
            <button
              onClick={() => {
                // Find the closest parent element with the scanner container
                const scannerContainer = document.querySelector('.scanner-container');
                if (scannerContainer) {
                  // Find the upload zone and simulate a click
                  const uploadZone = scannerContainer.querySelector('input[type="file"]');
                  if (uploadZone) {
                    (uploadZone as HTMLElement).click();
                  }
                }
              }}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition flex items-center justify-center gap-2"
            >
              <i className="fa-solid fa-upload"></i>
              Upload Image Instead
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`camera-container ${isFullscreen ? 'fixed inset-0 z-50 bg-black' : isMobile ? 'w-full' : 'w-[40vw] max-md:w-[80vw]'} border-2 border-gray-200 rounded-lg overflow-hidden`}>
      <div className={`relative ${isFullscreen ? 'h-full' : 'aspect-[4/3]'}`}>
        {cameraError ? (
          <div className="absolute inset-0 bg-red-50 flex flex-col items-center justify-center p-4 text-center">
            <div className="bg-red-100 p-4 rounded-full inline-block mb-4">
              <i className="fa-solid fa-camera-slash text-red-500 text-3xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Camera Error</h3>
            <p className="text-red-600 mb-4">{cameraError}</p>

            <div className="flex flex-col sm:flex-row gap-3 justify-center mt-2">
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition flex items-center justify-center gap-2"
              >
                <i className="fa-solid fa-rotate-right"></i>
                Try Again
              </button>

              <button
                onClick={() => {
                  // Find the upload zone and simulate a click
                  const uploadInput = document.querySelector('.upload-zone-input');
                  if (uploadInput) {
                    (uploadInput as HTMLElement).click();
                  }
                }}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition flex items-center justify-center gap-2"
              >
                <i className="fa-solid fa-upload"></i>
                Upload Instead
              </button>
            </div>
          </div>
        ) : (
          <>
            <Webcam
              audio={false}
              ref={webcamRef}
              screenshotFormat="image/jpeg"
              videoConstraints={videoConstraints}
              className="w-full h-full object-contain"
              style={{
                objectFit: isFullscreen ? 'contain' : 'cover',
                width: '100%',
                height: '100%',
                transform: 'none'
              }}
              onUserMediaError={(error) => {
                // Webcam error
                if (error instanceof DOMException) {
                  if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                    setCameraError("Camera access denied. Please enable camera permissions in your browser settings.");
                  } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
                    setCameraError("No camera found on this device.");
                  } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
                    setCameraError("Camera is already in use by another application.");
                  } else if (error.name === 'OverconstrainedError') {
                    setCameraError("Camera doesn't meet the required constraints.");
                  } else {
                    setCameraError(`Camera error: ${error.message || error.name}`);
                  }
                } else {
                  setCameraError(`Camera error: ${typeof error === 'object' && error !== null && 'message' in error ? (error as {message: string}).message : String(error)}`);
                }
              }}
              mirrored={false}
              screenshotQuality={0.92}
              forceScreenshotSourceSize={true}
              imageSmoothing={true}
            />

            {/* Card placement overlay */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className={`relative ${isFullscreen ? 'w-[60%] h-[80%]' : 'w-[85%] h-[60%]'} border-2 border-white rounded-md`}>
                {/* Corner markers */}
                <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-white"></div>
                <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-white"></div>
                <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-white"></div>
                <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-white"></div>

                {/* Guidance text */}
                <div className="absolute -bottom-8 left-0 right-0 text-center text-white text-sm font-medium bg-black bg-opacity-50 py-1 rounded">
                  Position card within frame
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      <div className={`p-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center ${isFullscreen ? 'fixed bottom-0 left-0 right-0 bg-opacity-90' : ''}`}>
        <button
          onClick={toggleFullscreen}
          className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition flex items-center gap-2"
        >
          <i className={`fa-solid ${isFullscreen ? 'fa-compress' : 'fa-expand'}`}></i>
          <span className="max-md:hidden">{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</span>
        </button>
        <button
          onClick={capturePhoto}
          disabled={disabled}
          className="bg-[#4DA1A9] text-white px-6 py-3 rounded-lg hover:bg-[#3C8A92] transition disabled:opacity-50 flex items-center gap-2 font-medium"
        >
          <i className="fa-solid fa-camera"></i>
          Capture Photo
        </button>
      </div>
    </div>
  );
};

export default UnifiedCamera;
