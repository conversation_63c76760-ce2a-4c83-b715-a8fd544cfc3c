// src/app/api/scan/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { OCRService } from '@/lib/ocr-service';
import { <PERSON>rror<PERSON><PERSON><PERSON> } from '@/lib/error-handler';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;

    // Validate file input
    if (!file || !(file instanceof Blob)) {
      return NextResponse.json(
        { success: false, error: "No valid image file provided" },
        { status: 400 }
      );
    }

    const result = await OCRService.scanSingle(file);

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    // Log error internally without exposing details
    console.error("API Error:", error);
    return NextResponse.json(
      ErrorHandler.createApiErrorResponse(error, "Processing failed. Please try again."),
      { status: 500 }
    );
  }
}