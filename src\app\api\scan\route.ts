// src/app/api/scan/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { OCRService } from '@/lib/ocr-service';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;

    // Validate file input
    if (!file || !(file instanceof Blob)) {
      return NextResponse.json(
        { success: false, error: "No valid image file provided" },
        { status: 400 }
      );
    }

    const result = await OCRService.scanSingle(file);

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error("API Error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "OCR processing failed",
      },
      { status: 500 }
    );
  }
}