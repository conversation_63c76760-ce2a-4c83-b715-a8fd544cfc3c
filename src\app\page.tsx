"use client";
import React, { Suspense } from "react";
import Link from "next/link";
import { useUser } from "@clerk/nextjs";
import Image from "next/image";

import { SignedIn, SignedOut, UserButton } from "@clerk/nextjs";

// Loading component
const HomeLoading = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#4DA1A9] mx-auto mb-4"></div>
      <h2 className="text-xl font-semibold text-gray-700">Loading...</h2>
    </div>
  </div>
);

// Home content component
const HomeContent = () => {
  const { user } = useUser();

  return (
    <>
      <button
        id="installBtn"
        style={{
          position: "fixed",
          bottom: "80px",
          right: "20px",
          zIndex: 1000,
          display: "none",
        }}
        className="bg-[#4DA1A9] text-white font-bold px-4 py-2 rounded-lg shadow-lg flex items-center gap-2"
      >
        <i className="fa-solid fa-download"></i>
        Install App
      </button>

      {user && (
        <a
          href="/dashboard"
          id="adminPanelBtn"
          style={{
            position: "fixed",
            bottom: "20px",
            right: "20px",
            zIndex: 1000,
          }}
          className="bg-[#065c77] text-white px-4 py-2 rounded shadow outline-2 outline-black hover:bg-[#054a5e] transition-colors"
        >
          Dashboard
        </a>
      )}
      <SignedOut>
        <section className="container mx-auto   mt-[-6.1rem] bg-[#f9f9f9]">
          {/* Scroll-snap container */}
          <div
            className="w-[100vw]   h-[100vh] overflow-y-auto scrollbar-hide  bg-[url(/images/drew-beamer-kUHfMW8awpE-unsplash.jpg)] bg-cover bg-[#ffffffa6] bg-blend-soft-light
    scroll-snap-type-y-mandatory scroll-snap-type: y mandatory;
    scroll-snap-align: start;
    scroll-snap-stop: always;"
          >
            <div
              className=" h-screen    p-8 mt-8  flex items-center flex-wrap w-full
      scroll-snap-align-start container mx-auto  flex-col gap-4 justify-center "
            >
              <div className="max-md:flex-col max-md:text-center flex justify-between items-center w-full">
                <Image
                  className=" m-auto w-[40vw] max-md:w-full p-8"
                  src="/2.png"
                  alt="OCR image"
                  width={800}
                  height={800}
                />
                <div className="flex  text-black justify-between gap-4 m-auto flex-col">
                  <h1 className="text-5xl  font-black leading-none  text-gray-900 select-none uppercase">
                    Welcome to <span className="text-[#4DA1A9]">KROTI</span>{" "}
                  </h1>
                  <p>
                    You are just one click away from a life full of coordination
                    and satisfaction.
                  </p>
                  <div className="flex max-md:justify-center gap-4">
                    <Link href="/sign-in">
                      <button className="bg-[#4DA1A9] text-white font-bold rounded-full mt-6 py-4 px-8 shadow-lg uppercase tracking-wider transition-all duration-500 cursor-pointer hover:scale-105 hover:bg-[#3C8A92]">
                        Sign In
                      </button>
                    </Link>
                    <Link href="/sign-up">
                      <button className="outline outline-[#4DA1A9] text-[#4DA1A9] font-bold rounded-full mt-6 py-4 px-8 shadow-lg uppercase tracking-wider transition-all duration-500 cursor-pointer hover:scale-105 hover:bg-gray-50">
                        Sign Up
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <h2 className="text-3xl font-bold text-center text-gray-800 mb-8 mx-auto">
                You Won't Believe You Need to Save Your Cards with KROTI—
                <span className="text-red-400 font-black uppercase">
                  Until You Hear This Story!
                </span>
              </h2>
            </div>
            {/* Slide 1 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2">
                <h4 className="text-3xl text-red-400 font-black mb-3 uppercase">
                  Chapter one : The Card Chaos
                </h4>
                <p className="text-gray-600   text-xl min-md:px-8">
                  Meet Muhammed, a busy entrepreneur who constantly meets new
                  clients and friends. Every handshake ends with a business card
                  in his pocket. Over time, those cards pile up—some in his
                  wallet, others stuffed in drawers or lost in his car.
                </p>
              </div>
              <div className=" md:w-1/3 max-md:w-[70vw] mx-auto flex justify-center items-center">
                <Image
                  width={1000}
                  height={1000}
                  src="/3.png"
                  alt="Muhammed meets new clients"
                />
              </div>
            </div>

            {/* Slide 2 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2 ">
                <h4 className="text-3xl text-red-400 font-black mb-3 uppercase">
                  Chapter two : The Endless Search
                </h4>
                <p className="text-gray-600 min-md:px-8">
                  Whenever Muhammed needs a contact, he sifts through stacks of
                  cards, hoping to find the right one. Sometimes, the card is
                  bent, faded, or completely missing. Important opportunities
                  slip away just because he can't find a simple number.
                </p>
              </div>
              <div className="w-full max-md:w-[70vw] md:w-1/3 m-auto flex justify-center items-center">
                <Image
                  width={800}
                  height={800}
                  src="/4.png"
                  alt="Alot of cards"
                />
              </div>
            </div>

            {/* Slide 3 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2">
                <h4 className="text-3xl text-[#4DA1A9] font-black mb-3 uppercase">
                  Chapter three : The Game-Changer
                </h4>
                <p className="text-gray-600 min-md:px-8">
                  One day, Muhammed discovers{" "}
                  <span className="text-[#4DA1A9] font-black">KROTI</span>With a
                  simple scan, the app reads, saves, and organizes his cards in
                  the cloud. No more searching, no more lost contacts—just a
                  quick login from his phone or laptop, and every card is right
                  there.
                </p>
              </div>
              <div className="w-full max-md:w-[70vw] m-auto md:w-1/3 flex justify-center items-center">
                <Image
                  width={800}
                  height={800}
                  src="/3-(3).png"
                  alt="finding about KROTI"
                />
              </div>
            </div>

            {/* Slide 3 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2 ">
                <h4 className="text-3xl text-[#4DA1A9] font-black mb-3 uppercase">
                  Chapter four: A new Life!
                </h4>
                <p className="text-gray-600 min-md:px-8 ">
                  Now, Muhammed never worries about losing a contact. Whether
                  he's at home or on the go, he just searches a name, and the
                  details appear in seconds. No clutter, no stress—just seamless
                  access anytime, anywhere.
                </p>
              </div>
              <div className="w-full max-md:w-[70vw] m-auto md:w-1/3  flex justify-center items-center">
                <Image
                  width={800}
                  height={800}
                  src="/6.png"
                  alt="Muhammed with better life"
                />
              </div>
            </div>
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="container mx-auto px-6 text-center ">
                <h2 className="text-4xl max-md:text-3xl font-bold text-center text-gray-800 ">
                  Stop losing important contacts.{" "}
                  <span className="text-[#4DA1A9] uppercase">
                    Start scanning now!
                  </span>
                </h2>
                <h3 className="my-4 text-2xl text-gray-600">
                  You are just one click away from a life full of coordination
                  and satisfaction.
                </h3>
                <div className="flex gap-4 justify-center items-center">
                  <Link href="/sign-up">
                    <button className="bg-[#4DA1A9] text-white font-bold rounded-full mt-6 py-4 px-8 shadow-lg uppercase tracking-wider transition-all duration-500 cursor-pointer hover:scale-105 hover:bg-[#3C8A92]">
                      Sign Up
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </SignedOut>

      <SignedIn>
        <section className="container  mt-[-5.2rem] mx-auto bg-[#f9f9f9]">
          {/* Scroll-snap container */}
          <div
            className="w-[100vw]  mx-auto h-[100vh] overflow-y-auto scrollbar-hide
    scroll-snap-type-y-mandatory scroll-snap-type: y mandatory; bg-[url(/images/drew-beamer-kUHfMW8awpE-unsplash.jpg)]  bg-cover bg-[#ffffffa6] bg-blend-soft-light
    scroll-snap-align: start;
    scroll-snap-stop: always;"
          >
            <div
              className=" h-screen  p-8 mt-8  flex items-center flex-wrap  w-[100vw]
      scroll-snap-align-start container    flex-col gap-4 justify-center  "
            >
              <div className="md:flex-row flex-col max-md:text-center flex justify-between items-center w-full">
                <Image
                  className=" m-auto w-1/2 max-md:w-full p-8 "
                  src="/welcoming.png"
                  alt="OCR image"
                  width={800}
                  height={800}
                />
                <div className="flex text-black justify-between gap-4 m-auto flex-col">
                  <h1 className="text-5xl  font-bold">
                    Welcome{" "}
                    <span className="text-[#4DA1A9]">
                      {user?.username || "User"}
                    </span>
                    <UserButton />
                  </h1>
                  <p>
                    Ready to leave your old problems behind and start scanning
                    your cards?
                  </p>
                  <Link href="/dashboard" legacyBehavior>
                    <div>
                      <a className="outline outline-black text-black font-bold py-2 px-4 rounded-full transition-all duration-500 cursor-pointer hover:scale-105">
                        Go to Dashboard
                      </a>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <h2 className="text-3xl font-bold text-center text-gray-800 mb-8 mx-auto">
                How to Get the Best Scan:
                <span className="text-[#4DA1A9] font-black uppercase">
                  A Quick Guide
                </span>
              </h2>
            </div>
            {/* Slide 1 */}
            <div
              className="h-screen  p-8 mt-8   flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full md:w-1/2">
                <h4 className="text-4xl text-[#4DA1A9] font-black mb-3 uppercase">
                  Perfect Lighting
                </h4>
                <p className="text-gray-600 mb-8 min-md:px-8  text-xl">
                  Ensure you have good lighting when scanning a card. Natural
                  light or a well-lit room works best. Avoid shadows and glare
                  for clear, readable text.
                </p>
              </div>
              <div className="w-full md:w-1/2 flex justify-center items-center">
                <Image
                  width={300}
                  height={300}
                  src="/light.svg"
                  alt="Muhammed meets new clients"
                />
              </div>
            </div>

            {/* Slide 2 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2 ">
                <h4 className="text-4xl text-[#4DA1A9] font-black mb-3 uppercase">
                  Steady Hands, Clear Image
                </h4>
                <p className="text-gray-600 min-md:px-8 mb-8 text-xl">
                  Hold your phone steady and align the card within the frame.
                  Blurry images can lead to incorrect text recognition, so take
                  a moment to focus before snapping.
                </p>
              </div>
              <div className="w-full md:w-1/2  flex justify-center items-center">
                <Image
                  width={300}
                  height={300}
                  src="/scan.svg"
                  alt="Alot of cards"
                />
              </div>
            </div>

            {/* Slide 3 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2">
                <h4 className="text-4xl text-[#4DA1A9] font-black mb-3 uppercase">
                  Flat Surface, Suitable Contrast{" "}
                </h4>
                <p className="text-gray-600 min-md:px-8 mb-8 text-xl">
                  Place the card on a flat surface with a contrasting background
                  (like a dark table for light cards). This helps the Kroti
                  detect the edges accurately.
                </p>
              </div>
              <div className="w-full md:w-1/2 flex justify-center items-center">
                <Image
                  width={300}
                  height={300}
                  src="/flat.svg"
                  alt="finding about KROTI"
                />
              </div>
            </div>

            {/* Slide 3 */}
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="w-full max-md:text-center md:w-1/2 ">
                <h4 className="text-4xl text-[#4DA1A9] font-black mb-3 uppercase">
                  Double-Check & Save
                </h4>
                <p className="text-gray-600 min-md:px-8 mb-8 text-xl">
                  After scanning, review the extracted text. If needed, make
                  quick edits before saving to ensure accuracy. Once saved, you
                  can access your cards anytime from your phone or laptop.
                </p>
              </div>
              <div className="w-full md:w-1/2  flex justify-center items-center">
                <Image
                  width={300}
                  height={300}
                  src="/check.svg"
                  alt="Muhammed with better life"
                />
              </div>
            </div>
            <div
              className="h-screen  p-8 mt-8  flex items-center flex-wrap mb-20
      scroll-snap-align-start"
            >
              <div className="container mx-auto px-6 text-center ">
                <h2 className="text-4xl font-bold text-center text-gray-800 mb-8">
                  Start scanning like a pro and{" "}
                  <span className="text-[#4DA1A9] uppercase">
                    never lose a contact again!
                  </span>
                </h2>
                <div className="flex gap-4 justify-center items-center transition-all duration-500 hover:scale-105">
                  <Link href="/dashboard" legacyBehavior>
                    <div>
                      <a className="outline outline-black text-black font-bold text-2xl py-2 px-4 rounded-full  cursor-pointer ">
                        Go to Dashboard
                      </a>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </SignedIn>
    </>
  );
};

// Main Home component with Suspense
export default function Home() {
  return (
    <Suspense fallback={<HomeLoading />}>
      <HomeContent />
    </Suspense>
  );
}
