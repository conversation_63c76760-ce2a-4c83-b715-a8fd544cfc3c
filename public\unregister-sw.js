// public/unregister-sw.js
// This script unregisters any existing service workers in development mode

if ('serviceWorker' in navigator && window.location.hostname === 'localhost') {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for (let registration of registrations) {
      registration.unregister();
      // Service Worker unregistered in development mode
    }
  });
}
