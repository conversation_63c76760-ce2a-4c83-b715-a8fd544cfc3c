// server.js
const http = require('http');
const https = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const useHttps = process.env.HTTPS === 'true';
const app = next({ dev });
const handle = app.getRequestHandler();

// Log the environment mode
console.log(`Server running in ${dev ? 'development' : 'production'} mode`);
console.log(`HTTPS is ${useHttps ? 'enabled' : 'disabled'}`);

// Function to create the request handler
const requestHandler = (req, res) => {
  const parsedUrl = parse(req.url, true);
  const { pathname } = parsedUrl;

  // In development mode, block service worker requests to prevent GenerateSW warnings
  if (dev && (pathname === '/sw.js' || pathname.startsWith('/workbox-'))) {
    res.statusCode = 404;
    res.end('Not found in development mode');
    return;
  }

  // Handle manifest.json specifically
  if (pathname === '/manifest.json') {
    res.setHeader('Content-Type', 'application/json');
    // Try multiple locations for the manifest.json file
    const manifestPaths = [
      path.join(__dirname, 'manifest.json'),
      path.join(__dirname, 'public', 'manifest.json'),
      path.join(__dirname, '.next', 'manifest.json')
    ];

    let manifestContent = null;

    for (const manifestPath of manifestPaths) {
      try {
        if (fs.existsSync(manifestPath)) {
          manifestContent = fs.readFileSync(manifestPath, 'utf8');
          break;
        }
      } catch (error) {
        console.error(`Error reading manifest from ${manifestPath}:`, error);
      }
    }

    if (manifestContent) {
      res.end(manifestContent);
    } else {
      console.error('Could not find manifest.json in any location');
      res.statusCode = 404;
      res.end('Not found');
    }
    return;
  }

  // Handle favicon.ico specifically
  if (pathname === '/favicon.ico') {
    res.setHeader('Content-Type', 'image/x-icon');
    // Try multiple locations for the favicon.ico file
    const faviconPaths = [
      path.join(__dirname, 'favicon.ico'),
      path.join(__dirname, 'public', 'favicon.ico'),
      path.join(__dirname, '.next', 'favicon.ico')
    ];

    let faviconContent = null;

    for (const faviconPath of faviconPaths) {
      try {
        if (fs.existsSync(faviconPath)) {
          faviconContent = fs.readFileSync(faviconPath);
          break;
        }
      } catch (error) {
        console.error(`Error reading favicon from ${faviconPath}:`, error);
      }
    }

    if (faviconContent) {
      res.end(faviconContent);
    } else {
      console.error('Could not find favicon.ico in any location');
      res.statusCode = 404;
      res.end('Not found');
    }
    return;
  }

  // Handle font files
  if (pathname.startsWith('/Fonts/') && pathname.endsWith('.ttf')) {
    const fontPath = path.join(__dirname, 'public', pathname);

    try {
      const fontContent = fs.readFileSync(fontPath);
      res.setHeader('Content-Type', 'font/ttf');
      res.end(fontContent);
    } catch (error) {
      console.error(`Error serving font file ${pathname}:`, error);
      res.statusCode = 404;
      res.end('Not found');
    }
    return;
  }

  // Let Next.js handle all other requests
  handle(req, res, parsedUrl);
};

app.prepare().then(() => {
  // Create the appropriate server based on HTTPS setting
  let server;

  if (useHttps) {
    try {
      // Check if certificate files exist
      const certDir = path.join(__dirname, 'certificates');
      const keyPath = path.join(certDir, 'localhost-key.pem');
      const certPath = path.join(certDir, 'localhost.pem');

      if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
        const httpsOptions = {
          key: fs.readFileSync(keyPath),
          cert: fs.readFileSync(certPath)
        };
        server = https.createServer(httpsOptions, requestHandler);
        console.log('Created HTTPS server with certificates');
      } else {
        console.warn('HTTPS certificates not found, falling back to HTTP');
        server = http.createServer(requestHandler);
      }
    } catch (error) {
      console.error('Error setting up HTTPS server:', error);
      console.log('Falling back to HTTP server');
      server = http.createServer(requestHandler);
    }
  } else {
    server = http.createServer(requestHandler);
  }

  // Start the server
  server.listen(3000, (err) => {
    if (err) throw err;
    console.log(`> Ready on ${useHttps ? 'https' : 'http'}://localhost:3000`);
  });
});
