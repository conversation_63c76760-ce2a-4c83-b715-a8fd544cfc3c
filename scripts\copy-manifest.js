// scripts/copy-manifest.js
const fs = require('fs');
const path = require('path');

// This script copies the manifest.json file to the _next directory
// to ensure it's properly served by Next.js

function copyManifest() {
  try {
    // Source manifest file
    const sourceManifest = path.join(process.cwd(), 'public', 'manifest.json');
    
    // Create _next directory if it doesn't exist
    const nextDir = path.join(process.cwd(), '.next');
    if (!fs.existsSync(nextDir)) {
      fs.mkdirSync(nextDir, { recursive: true });
    }
    
    // Destination path
    const destManifest = path.join(nextDir, 'manifest.json');
    
    // Copy the file
    fs.copyFileSync(sourceManifest, destManifest);
    
    console.log('✅ Successfully copied manifest.json to .next directory');
  } catch (error) {
    console.error('❌ Error copying manifest.json:', error);
  }
}

// Execute the function
copyManifest();
