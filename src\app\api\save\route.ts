// src/app/api/save/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Card } from '@/models/Card';
import connectDB from '@/lib/mongodb';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler';

export async function POST(req: NextRequest) {
  try {
    await connectDB();
    const body = await req.json();
    const { userId, name, title, company, phone, email, address, website, qrCode, rawText, imageUrl, notes } = body;

    if (!userId || !name || !title) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Transform array fields to strings by taking first element
    const transformedData = {
      userId,
      name: Array.isArray(name) ? name[0] : name,
      title: Array.isArray(title) ? title[0] : title,
      company: Array.isArray(company) ? company[0] : company,
      phone: Array.isArray(phone) ? phone[0] : phone,
      email: Array.isArray(email) ? email[0] : email,
      address: Array.isArray(address) ? address[0] : address,
      website: Array.isArray(website) ? website[0] : website,
      qrCode: Array.isArray(qrCode) ? qrCode[0] : qrCode,
      rawText,
      imageUrl,
      notes: notes || "", // Ensure notes is not undefined
    };

    try {
      const card = await Card.create(transformedData);
      return NextResponse.json({ success: true, data: card });
    } catch (error) {
      // Log error for development only
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating card:', error);
      }
      return NextResponse.json(
        ErrorHandler.createApiErrorResponse(error, 'Failed to save card. Please try again.'),
        { status: 500 }
      );
    }
  } catch (error) {
    // Log error for development only
    if (process.env.NODE_ENV === 'development') {
      console.error('Error saving card:', error);
    }
    return NextResponse.json(
      ErrorHandler.createApiErrorResponse(error, 'Failed to save card. Please try again.'),
      { status: 500 }
    );
  }
}
