"use client";
import React, { useState, useEffect, Suspense } from "react";
import { useUser } from "@clerk/nextjs";
import { useSearchParams } from "next/navigation";
import CardView from "@/components/CardView";
import UnifiedCamera from "@/components/UnifiedCamera";
import UploadZone from "@/components/UploadZone";
import SharePopup from "@/components/SharePopup";
import { OCRClient, OCRResponse } from "@/lib/ocr-client";
import Image from "next/image";
import Link from "next/link";

export interface OCRResults {
  _id?: string;
  createdAt?: string;
  name?: string;
  title?: string;
  phone?: string;
  email?: string;
  company?: string;
  address?: string;
  rawText?: string;
  imageUrl?: string;
  website?: string;
  qrCode?: string;
  notes?: string;
  [key: string]: unknown;
}

const DashboardeLoading = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#4DA1A9] mx-auto mb-4"></div>
      <h2 className="text-xl font-semibold text-gray-700">Loading...</h2>
    </div>
  </div>
);

interface SavedCard extends OCRResults {
  _id: string;
  createdAt: string;
}

const DashboardContent: React.FC = () => {
  const { user } = useUser();
  const searchParams = useSearchParams()!; // Using non-null assertion since we have Suspense

  const [ocrResults, setOcrResults] = useState<OCRResults | null>(null);
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [mode, setMode] = useState<"dashboard" | "scan" | "detail" | "processing">("dashboard");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showScanner, setShowScanner] = useState(true);
  const [toast, setToast] = useState<{ visible: boolean; message: string; type: 'success' | 'error' }>({ visible: false, message: '', type: 'success' });
  const [shareCardData, setShareCardData] = useState<OCRResults | null>(null);
  const [tooltip, setTooltip] = useState<{ visible: boolean; text: string; position: { x: number; y: number } }>({ visible: false, text: "", position: { x: 0, y: 0 } });

  // Search functionality state (moved from Navbar)
  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [startDate, setStartDate] = useState(searchParams.get("startDate") || "");
  const [endDate, setEndDate] = useState(searchParams.get("endDate") || "");

  useEffect(() => {
    if (user) {
      // Get search parameters
      const search = searchParams.get("search") || "";
      const startDate = searchParams.get("startDate") || "";
      const endDate = searchParams.get("endDate") || "";

      // Build query URL with search parameters
      let url = `/api/cards?userId=${user.id}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;
      if (startDate) url += `&startDate=${encodeURIComponent(startDate)}`;
      if (endDate) url += `&endDate=${encodeURIComponent(endDate)}`;

      // Fetch cards with filters
      fetch(url)
        .then((res) => res.json())
        .then((data) => {
          if (data.success) setSavedCards(data.data);
        })
        .catch((err) => console.error("Failed to fetch cards:", err));
    }
  }, [user, searchParams]);

  const processOCRResult = (result: {
    entities: Record<string, string | string[]>;
    text: string;
    error?: string;
  }): OCRResults => {
    // Helper function to ensure string values
    const ensureString = (value: string | string[] | undefined): string => {
      if (Array.isArray(value)) {
        return value.length > 0 ? value[0] : "";
      }
      return value || "";
    };

    // Create the result object with all fields explicitly initialized
    const processedResult: OCRResults = {
      name: ensureString(result.entities["Person Name"]) || "Unknown",
      title: ensureString(result.entities["Job Title"]) || "",
      company: ensureString(result.entities["Company Name"]) || "",
      phone: ensureString(result.entities["Phone Number"]) || "",
      email: ensureString(result.entities["Email Address"]) || "",
      address: ensureString(result.entities["Address"]) || "",
      website: ensureString(result.entities["Website"]) || "",
      qrCode: ensureString(result.entities["QR Code"]) || "",
      rawText: result.text,
      notes: "", // Initialize notes as empty string
      createdAt: new Date().toISOString(), // Initialize with current date
    };

    return processedResult;
  };

  const handleCapture = async (imageData: string) => {
    setLoading(true);
    setError(null);

    try {
      // Set processing mode to show appropriate loading message
      setMode("processing");

      // Small delay to ensure loading screen appears
      await new Promise(resolve => setTimeout(resolve, 500));

      const blob = await fetch(imageData).then((r) => r.blob());
      const file = new File([blob], "capture.jpg", { type: blob.type });
      const result: OCRResponse = await OCRClient.scanSingle(file);

      if (result.error) throw new Error(result.error);
      if (!result.data) throw new Error("No OCR data returned");

      const processedResults = processOCRResult(result.data);
      // Set the image URL from the captured image (data URL format)
      // Note: This data URL will be converted to a File and uploaded to Cloudinary when the card is saved
      processedResults.imageUrl = imageData;

      setOcrResults(processedResults);
      setShowScanner(false); // Hide scanner after successful capture
      // Keep in scan mode but with the edit form visible instead of switching to detail view
      setMode("scan");
    } catch (err) {
      console.error("OCR Error:", err);
      const errorMessage = err instanceof Error ? err.message : "OCR processing failed";
      setError(errorMessage);

      // Show error toast
      setToast({ visible: true, message: errorMessage, type: 'error' });
      setTimeout(() => setToast({ visible: false, message: '', type: 'error' }), 3000);

      // Return to scan mode
      setMode("scan");
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (file: File) => {
    setLoading(true);
    setError(null);

    try {
      // Set processing mode to show appropriate loading message
      setMode("processing");

      // Small delay to ensure loading screen appears
      await new Promise(resolve => setTimeout(resolve, 500));

      const result: OCRResponse = await OCRClient.scanSingle(file);
      if (result.error) throw new Error(result.error);
      if (!result.data) throw new Error("No OCR data returned");

      // Create a temporary blob URL for the uploaded file
      // Note: This is a temporary URL that will be replaced with a Cloudinary URL when the card is saved
      const imageUrl = URL.createObjectURL(file);
      const processedResults = processOCRResult(result.data);
      // Set the image URL from the uploaded file
      processedResults.imageUrl = imageUrl;

      setOcrResults(processedResults);
      setShowScanner(false); // Hide scanner after successful upload
      // Keep in scan mode but with the edit form visible instead of switching to detail view
      setMode("scan");
    } catch (err) {
      console.error("Upload Error:", err);
      const errorMessage = err instanceof Error ? err.message : "File processing failed";
      setError(errorMessage);

      // Show error toast
      setToast({ visible: true, message: errorMessage, type: 'error' });
      setTimeout(() => setToast({ visible: false, message: '', type: 'error' }), 3000);

      // Return to scan mode
      setMode("scan");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!ocrResults || !user) return;
    setLoading(true);
    setError(null);
    // Set processing mode to show appropriate loading/error screen
    setMode("processing");
    try {
      // Ensure notes field is initialized
      if (ocrResults.notes === undefined) {
        ocrResults.notes = "";
      }

      const isUpdate = !!ocrResults._id;
      const url = isUpdate ? `/api/cards/${ocrResults._id}` : "/api/save";
      const method = isUpdate ? "PUT" : "POST";

      // If we have an image URL (either data URL from camera or blob URL from upload),
      // we need to convert it to a file and upload it separately
      let imageToUpload = null;

      if (ocrResults.imageUrl) {
        if (ocrResults.imageUrl.startsWith('data:')) {
          // Convert data URL to File object
          const response = await fetch(ocrResults.imageUrl);
          const blob = await response.blob();
          imageToUpload = new File([blob], 'card-image.jpg', { type: 'image/jpeg' });
        } else if (ocrResults.imageUrl.startsWith('blob:')) {
          // Convert blob URL to File object
          try {
            const response = await fetch(ocrResults.imageUrl);
            const blob = await response.blob();
            imageToUpload = new File([blob], 'card-image.jpg', { type: blob.type || 'image/jpeg' });
          } catch (error) {
            console.error('Error converting blob URL to file:', error);
            // Continue without the image if there's an error
          }
        }
      }

      // Prepare data to be sent to the API
      // Create a new object with only the fields we need
      const dataToSend = {
        userId: user.id,
        name: ocrResults.name || "",
        title: ocrResults.title || "",
        company: ocrResults.company || "",
        phone: ocrResults.phone || "",
        email: ocrResults.email || "",
        address: ocrResults.address || "",
        website: ocrResults.website || "",
        qrCode: ocrResults.qrCode || "",
        rawText: ocrResults.rawText || "",
        imageUrl: ocrResults.imageUrl || "",
        notes: ocrResults.notes || "",
        createdAt: ocrResults.createdAt || new Date().toISOString()
      };

      // First save the card data
      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(dataToSend),
      });

      const data = await response.json();

      if (!data.success)
        throw new Error(data.error || "Failed to save card data");

      // Revoke blob URL if it exists to prevent memory leaks
      if (ocrResults.imageUrl && ocrResults.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(ocrResults.imageUrl);
      }

      // If we have an image to upload and the card was saved successfully
      if (imageToUpload && data.success && data.data._id) {
        try {
          // Now upload the image
          const formData = new FormData();
          formData.append('image', imageToUpload);

          // Important: When sending FormData, do NOT set Content-Type header
          // The browser will automatically set it with the correct boundary
          const imageResponse = await fetch(`/api/cards/${data.data._id}`, {
            method: 'PUT',
            body: formData,
          });

          if (!imageResponse.ok) {
            const errorText = await imageResponse.text();
            throw new Error(`HTTP error! status: ${imageResponse.status}, message: ${errorText}`);
          }

          const imageData = await imageResponse.json();
          if (!imageData.success) {
            console.warn('Image upload failed:', imageData.error);
            // We don't throw here because the card data was saved successfully
          }
        } catch (err) {
          console.error('Error uploading image:', err);
          // We don't throw here because the card data was saved successfully
        }
      }

      // Refresh saved cards list
      const res = await fetch(`/api/cards?userId=${user.id}`);
      const cardsData = await res.json();
      if (cardsData.success) setSavedCards(cardsData.data);

      // Show enhanced success toast
      setToast({
        visible: true,
        message: isUpdate ? 'Card updated successfully!' : 'New card saved successfully!',
        type: 'success'
      });
      setTimeout(() => setToast({ visible: false, message: '', type: 'success' }), 4000);

      setOcrResults(null);
      setShowScanner(true); // Reset scanner state
      setMode("dashboard");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to save card data";
      setError(errorMessage);

      // Show error toast
      setToast({ visible: true, message: errorMessage, type: 'error' });
      setTimeout(() => setToast({ visible: false, message: '', type: 'error' }), 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleCardClick = (card: SavedCard) => {
    // Ensure notes field is defined
    const cardWithNotes = {
      ...card,
      notes: card.notes || ""
    };
    setOcrResults(cardWithNotes);
    setMode("detail");
  };

  // Copy to clipboard function
  const copyToClipboard = (text: string, event: React.MouseEvent, tooltipText: string) => {
    event.preventDefault();
    navigator.clipboard.writeText(text)
      .then(() => {
        // Show tooltip
        const rect = (event.target as HTMLElement).getBoundingClientRect();
        setTooltip({
          visible: true,
          text: tooltipText,
          position: {
            x: rect.left + rect.width / 2,
            y: rect.top - 10
          }
        });

        // Hide tooltip after 2 seconds
        setTimeout(() => {
          setTooltip(prev => ({ ...prev, visible: false }));
        }, 2000);
      })
      .catch(err => console.error("Could not copy text: ", err));
  };

  // Reset showScanner when mode changes
  useEffect(() => {
    if (mode === "scan") {
      // Only show scanner if there are no OCR results
      // This ensures the edit form is shown after scanning
      setShowScanner(!ocrResults);
    }
  }, [mode, ocrResults]);

  // Effect to handle mode changes and ensure notes field is initialized
  useEffect(() => {
    if (ocrResults) {
      // Ensure notes is always initialized
      if (ocrResults.notes === undefined) {
        setOcrResults(prev => ({
          ...prev!,
          notes: ""
        }));
      }
    }
  }, [ocrResults]);

  // Processing mode with potential failure screen
  if (mode === "processing") {
    return (
      <div className="container mx-auto p-4 bg-gray-50 min-h-screen flex items-center justify-center">
        {/* Enhanced Toast Notification */}
        {toast.visible && (
          <div className={`fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-xl ${toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white transition-all duration-300 flex items-center gap-3 min-w-[300px] max-w-md`}>
            <div className="flex-shrink-0">
              {toast.type === 'success' ? (
                <i className="fa-solid fa-circle-check text-xl"></i>
              ) : (
                <i className="fa-solid fa-circle-exclamation text-xl"></i>
              )}
            </div>
            <div className="flex-grow">
              <p className="font-medium">{toast.message}</p>
            </div>
          </div>
        )}

        {loading ? (
          <div className="bg-white p-8 rounded-lg shadow-xl flex flex-col items-center max-w-md w-full">
            <div className="relative w-64 h-64 flex items-center justify-center">
              <div className="flex flex-col items-center">
                <div className="relative w-40 h-40 mb-4">
                  <div className="absolute inset-0 border-4 border-[#4DA1A9] border-t-transparent rounded-full animate-spin"></div>
                  <div className="absolute inset-2 border-4 border-[#80CBC4] border-b-transparent rounded-full animate-spin animation-delay-150"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <i className="fa-solid fa-camera text-[#4DA1A9] text-4xl animate-pulse"></i>
                  </div>
                </div>
              </div>
            </div>
            <p className="text-2xl font-semibold mt-2 text-center text-gray-800">Processing card...</p>
            <p className="text-gray-500 mt-2 text-center">Please wait while we save your card</p>
            <div className="mt-4 w-full">
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-[#4DA1A9] h-2.5 rounded-full animate-progress"></div>
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-white p-8 rounded-lg shadow-xl flex flex-col items-center max-w-md w-full">
            <div className="relative w-64 h-64 flex items-center justify-center">
              <div className="flex flex-col items-center">
                <div className="relative w-40 h-40 mb-4 flex items-center justify-center">
                  <i className="fa-solid fa-circle-exclamation text-red-500 text-6xl"></i>
                </div>
              </div>
            </div>
            <p className="text-2xl font-semibold mt-2 text-center text-gray-800">Oops! Something went wrong</p>
            <p className="text-gray-500 mt-2 text-center">We couldn't save your card. Please try scanning again.</p>
            <p className="text-red-500 text-sm mt-2 text-center">{error}</p>
            <div className="mt-6 flex gap-4">
              <button
                onClick={() => {
                  setMode("scan");
                  setShowScanner(true);
                  setError(null);
                }}
                className="px-6 py-2 bg-[#4DA1A9] text-white rounded-md hover:bg-[#3C8A92] transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => {
                  setMode("dashboard");
                  setError(null);
                }}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 transition-colors"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        ) : null}
      </div>
    );
  }

  if (mode === "scan") {

    return (
      <div className="container mx-auto p-4 bg-gray-50 min-h-screen">
        {/* Enhanced Toast Notification */}
        {toast.visible && (
          <div className={`fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-xl ${toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white transition-all duration-300 flex items-center gap-3 min-w-[300px] max-w-md`}>
            <div className="flex-shrink-0">
              {toast.type === 'success' ? (
                <i className="fa-solid fa-circle-check text-xl"></i>
              ) : (
                <i className="fa-solid fa-circle-exclamation text-xl"></i>
              )}
            </div>
            <div className="flex-grow">
              <p className="font-medium">{toast.message}</p>
            </div>
          </div>
        )}
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col items-center justify-center z-50">
            <div className="bg-white p-8 rounded-lg shadow-xl flex flex-col items-center max-w-md w-full">
              <div className="relative w-64 h-64 flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="relative w-40 h-40 mb-4">
                    <div className="absolute inset-0 border-4 border-[#4DA1A9] border-t-transparent rounded-full animate-spin"></div>
                    <div className="absolute inset-2 border-4 border-[#80CBC4] border-b-transparent rounded-full animate-spin animation-delay-150"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <i className="fa-solid fa-camera text-[#4DA1A9] text-4xl animate-pulse"></i>
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-2xl font-semibold mt-2 text-center text-gray-800">Scanning card...</p>
              <p className="text-gray-500 mt-2 text-center">Please wait while we process your image</p>
              <div className="mt-4 w-full">
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-[#4DA1A9] h-2.5 rounded-full animate-progress"></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Header with back button */}
        <div className="mb-6 flex justify-between items-center">
          <button
            onClick={() => {
              setMode("dashboard");
              setOcrResults(null);
            }}
            className="text-gray-800 border border-gray-300 rounded-full px-4 py-2  transition-colors"
          >
            Back to Dashboard
          </button>
          <h2 className="text-2xl max-md:text-xl font-bold text-gray-800 uppercase">
            {showScanner ? "Scan a Card" : "Edit Card Data"}
          </h2>
          <div className="min-md:w-[120px]"></div> {/* Spacer for alignment */}
        </div>

        {/* Scanner View */}
        {showScanner && (
          <div className="w-full flex flex-col items-center scanner-container">
            <div className="w-full max-w-xl mb-4 px-2">
              <UnifiedCamera
                onCapture={handleCapture}
                disabled={loading}
              />
            </div>
            <div className="w-full max-w-xl px-2">
              <UploadZone onUpload={handleUpload} disabled={loading} />
            </div>
          </div>
        )}

        {/* Edit Panel */}
        {ocrResults && !showScanner && (
          <section className="relative rounded-lg max-w-6xl mx-auto">
            {/* Two-column layout container */}
            <div className="flex flex-col md:flex-row bg-white rounded-lg overflow-hidden">
              {/* Left side - Card Image */}
              <div className="md:w-1/2 bg-white p-4 min-md:m-auto relative">
                {/* Image container with fixed aspect ratio */}
                <div className="relative w-full h-[30vh] md:h-[60vh] flex items-center justify-center">
                  {ocrResults.imageUrl ? (
                    <Image
                      src={ocrResults.imageUrl}
                      alt="Scanned Card"
                      fill
                      className="object-contain rounded-lg"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200 rounded-lg">
                      <p className="text-gray-500">No image available</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Right side - Card Data Form */}
              <div className="md:w-1/2 p-6 relative">
                {/* Back to Scan button */}
                <button
                  onClick={() => setShowScanner(true)}
                  className="absolute top-4 right-4 border-1 border-black text-black hover:bg-gray-300 text-sm px-3 py-1 rounded transition-colors"
                >
                  Back to Scan
                </button>

                <h3 className="text-2xl uppercase font-bold mb-6 text-gray-800 min-md:mt-8">Edit Card Data</h3>

                <form className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {Object.entries({
                    name: "Name",
                    title: "Title",
                    company: "Company",
                    phone: "Phone",
                    email: "Email",
                    website: "Website",
                    address: "Address",

                    qrCode: "QR Code",
                  }).map(([key, label]) => (
                    <div key={key} className={key === 'address' ? 'col-span-full' : ''}>
                      <label htmlFor={`card-${key}`} className="block text-sm font-medium text-black">{label}</label>
                      <input
                        id={`card-${key}`}
                        type="text"
                        value={(ocrResults[key] as string) || ""}
                        onChange={(e) =>
                          setOcrResults((prev) => ({
                            ...prev!,
                            [key]: e.target.value,
                          }))
                        }
                        className="mt-1 block w-full text-[#4DA1A9] border border-gray-300 rounded-md p-2 shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                  ))}
                     {/* Date field */}
                     <div className="col-span-full sm:col-span-1">
                    <label htmlFor="card-date" className="block text-sm font-medium text-black">Date Added</label>
                    <input
                      id="card-date"
                      type="date"
                      value={ocrResults.createdAt ? new Date(ocrResults.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}
                      onChange={(e) =>
                        setOcrResults((prev) => ({
                          ...prev!,
                          createdAt: e.target.value,
                        }))
                      }
                      className="mt-1 block w-full text-[#4DA1A9] border border-gray-300 rounded-md p-2 shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>

                  {/* Notes field - full width */}
                  <div className="col-span-full">
                    <label htmlFor="card-notes" className="block text-sm font-medium text-black">Notes (optional)</label>
                    <div>
                      <textarea
                        id="card-notes"
                        value={(ocrResults.notes as string) || ""}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          // Direct update to ensure the value is set immediately
                          ocrResults.notes = newValue;
                          setOcrResults({...ocrResults});
                        }}
                        placeholder="Add notes about where you met this person or any other details..."
                        className="mt-1 block w-full text-[#4DA1A9] border border-gray-300 rounded-md p-2 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px]"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        {ocrResults.notes ? `${(ocrResults.notes as string).length} characters` : "No notes added yet"}
                      </div>
                    </div>
                  </div>



                  {/* Save Button */}
                  <div className="col-span-full flex justify-end mt-6">
                    <button
                      type="button"
                      onClick={handleSave}
                      className="px-6 py-2 bg-[#4DA1A9] text-white rounded-md hover:bg-[#3C8A92] transition-colors"
                      disabled={loading}
                    >
                      {loading ? "Saving..." : "Save Card"}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </section>
        )}

        {error && <p className="text-red-500 text-center mt-4">{error}</p>}
      </div>
    );
  }

  if (mode === "detail" && ocrResults) {
    return (
      <div className="fixed inset-0 bg-black z-50 overflow-auto">
        {/* Enhanced Toast Notification */}
        {toast.visible && (
          <div className={`fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-xl ${toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white transition-all duration-300 flex items-center gap-3 min-w-[300px] max-w-md`}>
            <div className="flex-shrink-0">
              {toast.type === 'success' ? (
                <i className="fa-solid fa-circle-check text-xl"></i>
              ) : (
                <i className="fa-solid fa-circle-exclamation text-xl"></i>
              )}
            </div>
            <div className="flex-grow">
              <p className="font-medium">{toast.message}</p>
            </div>
          </div>
        )}
        {loading && <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col items-center justify-center z-50">
          <div className="bg-black p-8 rounded-lg flex flex-col items-center max-w-md w-full">
            <div className="relative w-64 h-64 flex items-center justify-center">
              <div className="flex flex-col items-center">
                <div className="relative w-40 h-40 mb-4">
                  <div className="absolute inset-0 border-4 border-[#4DA1A9] border-t-transparent rounded-full animate-spin"></div>
                  <div className="absolute inset-2 border-4 border-[#80CBC4] border-b-transparent rounded-full animate-spin animation-delay-150"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <i className="fa-solid fa-sync text-[#4DA1A9] text-4xl animate-pulse"></i>
                  </div>
                </div>
              </div>
            </div>
            <p className="text-2xl font-semibold mt-2 text-center text-white">Processing...</p>
            <p className="text-gray-400 mt-2 text-center">Please wait while we update your card</p>
            <div className="mt-4 w-full">
              <div className="w-full bg-gray-700 rounded-full h-2.5">
                <div className="bg-[#4DA1A9] h-2.5 rounded-full animate-progress"></div>
              </div>
            </div>
          </div>
        </div>}
        <div className="mx-auto">


          {/* Card detail view */}
          <CardView
            data={{
              ...ocrResults,
              name: ocrResults.name || "Unknown",
              title: ocrResults.title || "",
              notes: typeof ocrResults.notes === 'string' ? ocrResults.notes : "", // Ensure notes is a string
            }}
            onBack={() => setMode("dashboard")}
            onEdit={() => {
              setMode("scan");
              setShowScanner(false); // Ensure scanner is hidden to show edit form
            }}
            onDelete={async () => {
              if (!ocrResults._id) return;
              setLoading(true);
              try {
                const res = await fetch(`/api/cards/${ocrResults._id}?userId=${user?.id}`, {
                  method: "DELETE",
                });
                const data = await res.json();
                if (!data.success) {
                  throw new Error(data.error || "Failed to delete card");
                }

                // Refresh saved cards list
                const fetchRes = await fetch(`/api/cards?userId=${user?.id}`);
                const cardsData = await fetchRes.json();
                if (cardsData.success) {
                  setSavedCards(cardsData.data);
                  setMode("dashboard");
                } else {
                  throw new Error("Failed to refresh cards list");
                }
              } catch (err) {
                setError(err instanceof Error ? err.message : "Delete failed");
              } finally {
                setLoading(false);
              }
            }}
            onUpdateImage={(e) => {
              if (e.target.files && e.target.files[0] && ocrResults?._id) {
                setLoading(true);
                const formData = new FormData();
                formData.append("image", e.target.files[0]);
                // Important: When sending FormData, do NOT set Content-Type header
                // The browser will automatically set it with the correct boundary
                fetch(`/api/cards/${ocrResults._id}`, {
                  method: "PUT",
                  body: formData,
                })
                  .then(async res => {
                    if (!res.ok) {
                      const errorText = await res.text();
                      throw new Error(`HTTP error! status: ${res.status}, message: ${errorText}`);
                    }
                    return res.json();
                  })
                  .then((data) => {
                    if (data.success) {
                      // Update local state with the new imageUrl
                      setOcrResults({ ...ocrResults, imageUrl: data.data.imageUrl });
                      // Optionally refresh your saved cards list
                    } else {
                      setError(data.error);
                    }
                  })
                  .catch((err) => setError(err.message))
                  .finally(() => setLoading(false));
              }
            }}
            isLoading={loading}
          />
          {error && <p className="text-red-500 text-center mt-4">{error}</p>}
        </div>
      </div>
    );
  }

  // Default Dashboard Grid View
  return (

      <div className="relative container mx-auto p-4 flex flex-col items-center">
      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="fixed bg-black text-white px-3 py-1 rounded text-sm z-50 transform -translate-x-1/2 opacity-90 transition-opacity duration-300"
          style={{
            left: `${tooltip.position.x}px`,
            top: `${tooltip.position.y}px`,
          }}
          role="status"
          aria-live="polite"
        >
          {tooltip.text}
          <div className="absolute w-2 h-2 bg-black transform rotate-45 -bottom-1 left-1/2 -translate-x-1/2"></div>
        </div>
      )}

      {/* Share Popup */}
      {shareCardData && (
        <SharePopup
          data={shareCardData}
          onClose={() => setShareCardData(null)}
          onCopy={copyToClipboard}
        />
      )}
      {/* Enhanced Toast Notification */}
      {toast.visible && (
        <div className={`fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-xl ${toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white transition-all duration-300 flex items-center gap-3 min-w-[300px] max-w-md`}>
          <div className="flex-shrink-0">
            {toast.type === 'success' ? (
              <i className="fa-solid fa-circle-check text-xl"></i>
            ) : (
              <i className="fa-solid fa-circle-exclamation text-xl"></i>
            )}
          </div>
          <div className="flex-grow">
            <p className="font-medium">{toast.message}</p>
          </div>
        </div>
      )}
      {loading && <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col items-center justify-center z-50">
        <div className="bg-white p-8 rounded-lg shadow-xl flex flex-col items-center max-w-md w-full">
          <div className="relative w-64 h-64 flex items-center justify-center">
            {showScanner ? (
              <div className="flex flex-col items-center">
                <div className="relative w-40 h-40 mb-4">
                  <div className="absolute inset-0 border-4 border-[#4DA1A9] border-t-transparent rounded-full animate-spin"></div>
                  <div className="absolute inset-2 border-4 border-[#80CBC4] border-b-transparent rounded-full animate-spin animation-delay-150"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <i className="fa-solid fa-id-card text-[#4DA1A9] text-4xl animate-pulse"></i>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <div className="relative w-40 h-40 mb-4">
                  <div className="absolute inset-0 border-4 border-[#4DA1A9] border-t-transparent rounded-full animate-spin"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <i className="fa-solid fa-database text-[#4DA1A9] text-4xl animate-pulse"></i>
                  </div>
                </div>
              </div>
            )}
          </div>
          <p className="text-2xl font-semibold mt-2 text-center text-gray-800">
            {showScanner ? "Processing your card..." : "Loading cards..."}
          </p>
          <p className="text-gray-500 mt-2 text-center">
            {showScanner ? "Please wait while we extract the information" : "Retrieving your saved cards"}
          </p>
          {showScanner && (
            <div className="mt-4 flex items-center justify-center">
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-[#4DA1A9] h-2.5 rounded-full animate-progress"></div>
              </div>
            </div>
          )}
        </div>
      </div>}
      <h1 className="text-4xl max-md:text-3xl font-bold mb-4 text-black">
        Welcome,{" "}
        <span className="text-[#4DA1A9]">{user?.username || "User"}</span>
      </h1>

      {/* Search bar moved from navbar */}
      <div className="w-full max-w-xl mb-6">
        <form onSubmit={(e) => {
          e.preventDefault();
          const params = new URLSearchParams();

          if (searchTerm) params.set("search", searchTerm);
          if (startDate) params.set("startDate", startDate);
          if (endDate) params.set("endDate", endDate);

          window.location.href = `/dashboard?${params.toString()}`;
        }} className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <div className="relative flex-grow">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search cards by name or company..."
                className="w-full px-4 py-2 border border-gray-300 text-black rounded-full focus:outline-none focus:ring-2 focus:ring-[#4DA1A9] text-sm"
              />
              {searchTerm && (
                <button
                  type="button"
                  onClick={() => setSearchTerm("")}
                  className="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <i className="fa-solid fa-times"></i>
                </button>
              )}
              <button
                type="submit"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <i className="fa-solid fa-search"></i>
              </button>
            </div>

            <button
              type="button"
              onClick={() => setShowDateFilter(!showDateFilter)}
              className={`p-2 rounded-full ${showDateFilter ? 'bg-[#4DA1A9] text-white' : 'bg-gray-100 text-gray-600'} hover:bg-opacity-90`}
              title="Filter by date"
            >
              <i className="fa-solid fa-calendar"></i>
            </button>

            {(searchParams.get("search") || searchParams.get("startDate") || searchParams.get("endDate")) && (
              <button
                type="button"
                onClick={() => {
                  setSearchTerm("");
                  setStartDate("");
                  setEndDate("");
                  window.location.href = "/dashboard";
                }}
                className="text-sm text-gray-600 hover:text-[#4DA1A9]"
              >
                Clear
              </button>
            )}
          </div>

          {/* Date filter */}
          {showDateFilter && (
            <div className="mt-2 flex flex-wrap gap-2 items-center bg-gray-50 p-2 rounded-md">
              <div>
                <label className="text-xs text-gray-600 block">From:</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="border border-gray-300 rounded p-1 text-sm"
                />
              </div>
              <div>
                <label className="text-xs text-gray-600 block">To:</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="border border-gray-300 rounded p-1 text-sm"
                />
              </div>
              <button
                type="submit"
                className="bg-[#4DA1A9] text-white px-3 py-1 rounded text-sm hover:bg-opacity-90 mt-auto"
              >
                Apply
              </button>
            </div>
          )}
        </form>
      </div>

      {/* Display search/filter status */}
      {(searchParams.get("search") || searchParams.get("startDate") || searchParams.get("endDate")) ? (
        <div className="text-lg text-gray-600 p-4 flex items-center gap-2">
          <span>Filtered results:</span>
          <span className="text-sm bg-gray-100 px-2 py-1 rounded-full">
            {searchParams.get("search") && `Search: "${searchParams.get("search")}"`}
            {searchParams.get("startDate") && ` From: ${new Date(searchParams.get("startDate") || "").toLocaleDateString()}`}
            {searchParams.get("endDate") && ` To: ${new Date(searchParams.get("endDate") || "").toLocaleDateString()}`}
          </span>
          <span className="text-sm text-gray-500">{savedCards.length} results</span>
        </div>
      ) : (
       <div className="w-full">
         <div className="flex flex-col md:flex-row justify-between items-center p-4">
           <p className="text-lg text-gray-600">Your saved cards:</p>
           <div className="text-sm text-gray-500 flex items-center gap-2 mt-2 md:mt-0">
             <i className="fa-solid fa-info-circle text-[#4DA1A9]"></i>
             <span>If you don't know how to scan, </span>
             <Link href="/" className="text-[#4DA1A9] hover:underline font-medium">
               view home
             </Link>
           </div>
         </div>
       </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 w-full">
        {savedCards.map((card) => (
          <figure
            key={card._id}
            className="relative overflow-hidden h-48 w-full bg-[#0b4257] cursor-pointer rounded-sm shadow-md hover:shadow-xl transition-all duration-300 group"
            onClick={() => handleCardClick(card)}
          >
            <div className="relative w-full h-full">
              <Image
                src={card.imageUrl || "/placeholder.jpg"}
                alt={card.name || "Business Card"}
                fill
                className="object-cover opacity-70 group-hover:opacity-40 group-hover:blur-xs transition-all duration-500"
              />
            </div>
            <figcaption className="absolute bottom-0 left-0 w-full bg-white text-[#3c4a50] p-4 h-16 transform max-md:translate-y-0 translate-y-full group-hover:translate-y-0 transition-transform duration-300 ease-in-out">
              <h2 className="float-left text-lg font-semibold transform translate-y-full max-md:translate-y-0 group-hover:translate-y-0 transition-transform duration-300 delay-[50ms]">{card.name || "Unknown"}</h2>
              <div className="float-right transform translate-y-full max-md:translate-y-0 group-hover:translate-y-0 transition-transform duration-300 delay-100">
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click
                    // Set the selected card data for sharing
                    setShareCardData(card);
                  }}
                  className="text-[#3c4a50] text-xl hover:text-[#252d31] flex items-center gap-1 px-2 py-1 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Share card"
                >
                  <i className="fa-solid fa-share-alt"></i>
                  <span className="text-sm">Share</span>
                </button>
              </div>
              <p className="absolute bottom-[5rem] left-0 w-full p-4 text-white opacity-0 max-md:opacity-100 group-hover:opacity-100 transition-opacity duration-300 text-sm">
                {card.company || "No Company"}{card.title ? ` • ${card.title}` : ""}
              </p>
            </figcaption>
          </figure>
        ))}
      </div>

      {/* Add Card Manually Button */}
      <button
        onClick={() => {
          // Create empty card data and go to edit mode
          setOcrResults({
            name: "",
            title: "",
            company: "",
            phone: "",
            email: "",
            address: "",
            website: "",
            qrCode: "",
            notes: "",
            createdAt: new Date().toISOString(),
          });
          setMode("scan");
          setShowScanner(false);
        }}
        className="fixed cursor-pointer bottom-[7rem] right-8 px-4 py-2 rounded-lg font-medium border border-gray-800 text-black hover:scale-105 transition-all"
      >
        Add Card Manually
      </button>

      {/* Scan New Card Button */}
      <button
        onClick={() => setMode("scan")}
        className="fixed cursor-pointer hover:scale-105 uppercase transition-transform bottom-8 right-8 bg-[#4DA1A9] text-white px-6 py-4 rounded-lg shadow-xl overflow-hidden group"
      >
        <span className="relative z-10">Scan a New Card</span>
        {/* Glow effect */}
        <span className="absolute inset-0 shadow-glow-blue animate-flicker opacity-70 group-hover:opacity-100"></span>
      </button>
      </div>

  );
};




export default function Dashboard() {
  return (
    <Suspense fallback={<DashboardeLoading />}>
      <DashboardContent />
    </Suspense>
  );
}
