"use client";
import React, { useState } from 'react';

const NotesTest: React.FC = () => {
  const [notes, setNotes] = useState('');
  const [savedNotes, setSavedNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    setLoading(true);
    try {
      // Create a test card with just notes
      const response = await fetch('/api/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: 'test-user',
          name: 'Test Card',
          title: 'Test Title',
          notes: notes
        })
      });

      const data = await response.json();
      // Log response for development only
      if (process.env.NODE_ENV === 'development') {
        console.log('API Response:', data);
      }
      
      if (data.success) {
        setSavedNotes(data.data.notes || '');
        alert('Notes saved successfully!');
      } else {
        alert('Failed to save notes: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      // Log error for development only
      if (process.env.NODE_ENV === 'development') {
        console.error('Error saving notes:', error);
      }
      alert('Error saving notes: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-xl shadow-md">
      <h2 className="text-xl font-bold mb-4">Notes Test</h2>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Enter Notes:
        </label>
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          rows={4}
          placeholder="Enter test notes here..."
        />
      </div>
      
      <button
        onClick={handleSave}
        disabled={loading}
        className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        {loading ? 'Saving...' : 'Save Notes'}
      </button>
      
      {savedNotes && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2">Saved Notes:</h3>
          <div className="p-3 bg-gray-100 rounded-md">
            {savedNotes}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotesTest;
